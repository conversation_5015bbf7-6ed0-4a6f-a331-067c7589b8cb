{"_format": "hh-sol-cache-2", "files": {"C:\\Users\\<USER>\\.cursor\\the-kings-dex\\contracts\\interfaces\\IERC20.sol": {"lastModificationDate": 1749258782667, "contentHash": "fde648c1aa358aa79bfe230185a35aab", "sourceName": "contracts/interfaces/IERC20.sol", "solcConfig": {"version": "0.6.6", "settings": {"optimizer": {"enabled": true, "runs": 999999}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.5.0"], "artifacts": ["IERC20"]}, "C:\\Users\\<USER>\\.cursor\\the-kings-dex\\contracts\\UniswapV2Router02.sol": {"lastModificationDate": 1749259037729, "contentHash": "febfabf32b7029fd6c46f2721b66ef52", "sourceName": "contracts/UniswapV2Router02.sol", "solcConfig": {"version": "0.6.6", "settings": {"optimizer": {"enabled": true, "runs": 999999}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./interfaces/IUniswapV2Factory.sol", "./libraries/TransferHelper.sol", "./interfaces/IUniswapV2Router02.sol", "./libraries/UniswapV2Library.sol", "./libraries/SafeMath.sol", "./interfaces/IERC20.sol", "./interfaces/IWETH.sol"], "versionPragmas": ["=0.6.6"], "artifacts": ["UniswapV2Router02"]}, "C:\\Users\\<USER>\\.cursor\\the-kings-dex\\contracts\\interfaces\\IUniswapV2Factory.sol": {"lastModificationDate": 1749258743583, "contentHash": "2209698f6fea1e6867a73b9932a8b11e", "sourceName": "contracts/interfaces/IUniswapV2Factory.sol", "solcConfig": {"version": "0.6.6", "settings": {"optimizer": {"enabled": true, "runs": 999999}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.5.0"], "artifacts": ["IUniswapV2Factory"]}, "C:\\Users\\<USER>\\.cursor\\the-kings-dex\\contracts\\libraries\\TransferHelper.sol": {"lastModificationDate": 1749258941958, "contentHash": "e3450647b47bb6e0337143c2f815000a", "sourceName": "contracts/libraries/TransferHelper.sol", "solcConfig": {"version": "0.6.6", "settings": {"optimizer": {"enabled": true, "runs": 999999}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.6.0"], "artifacts": ["TransferHelper"]}, "C:\\Users\\<USER>\\.cursor\\the-kings-dex\\contracts\\interfaces\\IUniswapV2Router02.sol": {"lastModificationDate": 1749258775993, "contentHash": "540c5034d259bbfd839db6da81629ad6", "sourceName": "contracts/interfaces/IUniswapV2Router02.sol", "solcConfig": {"version": "0.6.6", "settings": {"optimizer": {"enabled": true, "runs": 999999}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IUniswapV2Router01.sol"], "versionPragmas": [">=0.6.2"], "artifacts": ["IUniswapV2Router02"]}, "C:\\Users\\<USER>\\.cursor\\the-kings-dex\\contracts\\libraries\\UniswapV2Library.sol": {"lastModificationDate": 1749258932462, "contentHash": "d3d596c98fab637f55246babea6a323b", "sourceName": "contracts/libraries/UniswapV2Library.sol", "solcConfig": {"version": "0.6.6", "settings": {"optimizer": {"enabled": true, "runs": 999999}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../interfaces/IUniswapV2Pair.sol", "./SafeMath.sol"], "versionPragmas": [">=0.5.0"], "artifacts": ["UniswapV2Library"]}, "C:\\Users\\<USER>\\.cursor\\the-kings-dex\\contracts\\libraries\\SafeMath.sol": {"lastModificationDate": 1749258796726, "contentHash": "414854583787923b46443f2194922aa6", "sourceName": "contracts/libraries/SafeMath.sol", "solcConfig": {"version": "0.6.6", "settings": {"optimizer": {"enabled": true, "runs": 999999}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["=0.6.6"], "artifacts": ["SafeMath"]}, "C:\\Users\\<USER>\\.cursor\\the-kings-dex\\contracts\\interfaces\\IWETH.sol": {"lastModificationDate": 1749258789605, "contentHash": "90eee852810cec7aa8af6f2df8a7cf4c", "sourceName": "contracts/interfaces/IWETH.sol", "solcConfig": {"version": "0.6.6", "settings": {"optimizer": {"enabled": true, "runs": 999999}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.5.0"], "artifacts": ["IWETH"]}, "C:\\Users\\<USER>\\.cursor\\the-kings-dex\\contracts\\interfaces\\IUniswapV2Router01.sol": {"lastModificationDate": 1749258767379, "contentHash": "2bbb1a095415e36fa2e120c6072bdeee", "sourceName": "contracts/interfaces/IUniswapV2Router01.sol", "solcConfig": {"version": "0.6.6", "settings": {"optimizer": {"enabled": true, "runs": 999999}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.6.2"], "artifacts": ["IUniswapV2Router01"]}, "C:\\Users\\<USER>\\.cursor\\the-kings-dex\\contracts\\interfaces\\IUniswapV2Pair.sol": {"lastModificationDate": 1749258755134, "contentHash": "ceb53e4d503d769a0372c9dec32b031d", "sourceName": "contracts/interfaces/IUniswapV2Pair.sol", "solcConfig": {"version": "0.6.6", "settings": {"optimizer": {"enabled": true, "runs": 999999}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.5.0"], "artifacts": ["IUniswapV2Pair"]}, "C:\\Users\\<USER>\\.cursor\\the-kings-dex\\contracts\\UniswapV2Pair.sol": {"lastModificationDate": 1749258912380, "contentHash": "c48de60259e128e4176f4d9e3af66987", "sourceName": "contracts/UniswapV2Pair.sol", "solcConfig": {"version": "0.6.6", "settings": {"optimizer": {"enabled": true, "runs": 999999}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./interfaces/IUniswapV2Pair.sol", "./libraries/SafeMath.sol", "./libraries/Math.sol", "./libraries/UQ112x112.sol", "./interfaces/IERC20.sol", "./interfaces/IUniswapV2Factory.sol"], "versionPragmas": ["=0.6.6"], "artifacts": ["IUniswapV2Callee", "UniswapV2Pair"]}, "C:\\Users\\<USER>\\.cursor\\the-kings-dex\\contracts\\libraries\\Math.sol": {"lastModificationDate": 1749258803468, "contentHash": "fb719f060ad023edd05e53e04a55b354", "sourceName": "contracts/libraries/Math.sol", "solcConfig": {"version": "0.6.6", "settings": {"optimizer": {"enabled": true, "runs": 999999}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["=0.6.6"], "artifacts": ["Math"]}, "C:\\Users\\<USER>\\.cursor\\the-kings-dex\\contracts\\libraries\\UQ112x112.sol": {"lastModificationDate": 1749258810499, "contentHash": "8772fe0774ae6f20248218b943f647ac", "sourceName": "contracts/libraries/UQ112x112.sol", "solcConfig": {"version": "0.6.6", "settings": {"optimizer": {"enabled": true, "runs": 999999}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["=0.6.6"], "artifacts": ["UQ112x112"]}, "C:\\Users\\<USER>\\.cursor\\the-kings-dex\\contracts\\UniswapV2Factory.sol": {"lastModificationDate": 1749258820991, "contentHash": "cf1fbb09016434f87e33de620549fa90", "sourceName": "contracts/UniswapV2Factory.sol", "solcConfig": {"version": "0.6.6", "settings": {"optimizer": {"enabled": true, "runs": 999999}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./interfaces/IUniswapV2Factory.sol", "./UniswapV2Pair.sol"], "versionPragmas": ["=0.6.6"], "artifacts": ["UniswapV2Factory"]}}}