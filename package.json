{"name": "kings-dex-uniswap-v2", "version": "1.0.0", "description": "Uniswap V2 contracts for The King's Dex on Ronin", "main": "index.js", "scripts": {"compile": "hardhat compile", "deploy:saigon": "hardhat run scripts/deploy-hardhat.js --network ronin_saigon", "deploy:mainnet": "hardhat run scripts/deploy-hardhat.js --network ronin_mainnet", "test": "hardhat test", "node": "hardhat node", "verify-setup": "node scripts/verify-setup.js", "update-config:saigon": "node scripts/update-contract-addresses.js saigon", "update-config:mainnet": "node scripts/update-contract-addresses.js mainnet"}, "dependencies": {"firebase": "^11.8.1", "dotenv": "^16.3.1"}, "devDependencies": {"@nomiclabs/hardhat-ethers": "^2.2.3", "@nomiclabs/hardhat-waffle": "^2.0.6", "chai": "^4.3.10", "ethereum-waffle": "^4.0.10", "ethers": "^5.7.2", "hardhat": "^2.19.4"}, "keywords": ["uniswap", "dex", "ronin", "defi", "smart-contracts"], "author": "The King's Dex Team", "license": "GPL-3.0"}