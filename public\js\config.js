// Configuration for The King's Dex
// Network and contract configurations

// Chain IDs
const RONIN_CHAIN_ID_MAINNET = 2020;
const RONIN_CHAIN_ID_SAIGON = 2021; // Testnet

// Current target network (change this to switch between mainnet and testnet)
const TARGET_CHAIN_ID = RONIN_CHAIN_ID_SAIGON; // Using Saigon testnet for development
const TARGET_CHAIN_ID_HEX = '0x' + TARGET_CHAIN_ID.toString(16);

// RPC URLs
const RPC_URLS = {
    [RONIN_CHAIN_ID_MAINNET]: 'https://api.roninchain.com/rpc',
    [RONIN_CHAIN_ID_SAIGON]: 'https://saigon-testnet.roninchain.com/rpc'
};

// Contract Addresses
const CONTRACT_ADDRESSES = {
    [RONIN_CHAIN_ID_MAINNET]: {
        // Uniswap V2 contracts (to be deployed)
        UNISWAP_V2_FACTORY: '0xYOUR_UNISWAP_V2_FACTORY_MAINNET_ADDRESS',
        UNISWAP_V2_ROUTER: '0xYOUR_UNISWAP_V2_ROUTER_MAINNET_ADDRESS',
        
        // Katana DEX contracts (existing)
        KATANA_FACTORY: '0x7d0556d55ca1a92708681e2e231733ebd922597d',
        KATANA_ROUTER: '0x7d0556d55ca1a92708681e2e231733ebd922597d',
        
        // Token addresses
        WRON: '0xc99a6a985ed2cac1ef41640596c5a5f9f4e19ef5',
        AXS: '0x97a9107c179342bdcfa5f8e6768efd527dfb58e4',
        SLP: '0xa8754b9fa15fc18bb594588158ec0541a3adacd9',
        USDC: '0x0b7007c13325c48911f73a2dad5fa5dcbf808adc'
    },
    
    [RONIN_CHAIN_ID_SAIGON]: {
        // Uniswap V2 contracts (deployed to Saigon testnet)
        UNISWAP_V2_FACTORY: '0x3b34882989cF524B062470De68D1F4C053C9E98B',
        UNISWAP_V2_ROUTER: '0x64edE7475b34511F124Db708941A7767aeeD9a07',
        
        // Katana DEX contracts (not available on testnet)
        KATANA_FACTORY: null,
        KATANA_ROUTER: null,
        
        // Token addresses (testnet)
        WRON: '0xA959726154953bAe111746E265E6d754F48570E6',
        AXS: '0x3c4e17b9056272ce1b49f6900d8cfd6171a1869d',
        SLP: '0xa8754b9fa15fc18bb594588158ec0541a3adacd9',
        USDC: '0x067fbff8990c58ab90bae3c97241c5d736053f77'
    }
};

// Get current network contracts
const CURRENT_CONTRACTS = CONTRACT_ADDRESSES[TARGET_CHAIN_ID];

// Router priority (Uniswap V2 first, then Katana as fallback)
const ROUTER_PRIORITY = [
    {
        name: 'Uniswap V2',
        factory: CURRENT_CONTRACTS.UNISWAP_V2_FACTORY,
        router: CURRENT_CONTRACTS.UNISWAP_V2_ROUTER,
        fee: 0.003, // 0.3%
        available: !!CURRENT_CONTRACTS.UNISWAP_V2_ROUTER
    },
    {
        name: 'Katana DEX',
        factory: CURRENT_CONTRACTS.KATANA_FACTORY,
        router: CURRENT_CONTRACTS.KATANA_ROUTER,
        fee: 0.0025, // 0.25%
        available: !!CURRENT_CONTRACTS.KATANA_ROUTER
    }
];

// Firebase Configuration
const FIREBASE_CONFIG = {
    apiKey: "your-api-key",
    authDomain: "your-project.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project.appspot.com",
    messagingSenderId: "123456789",
    appId: "your-app-id"
};

// Explorer URLs
const EXPLORER_URLS = {
    [RONIN_CHAIN_ID_MAINNET]: 'https://app.roninchain.com',
    [RONIN_CHAIN_ID_SAIGON]: 'https://saigon-app.roninchain.com'
};

// Network Names
const NETWORK_NAMES = {
    [RONIN_CHAIN_ID_MAINNET]: 'Ronin Mainnet',
    [RONIN_CHAIN_ID_SAIGON]: 'Ronin Saigon Testnet'
};

// Default slippage tolerance (0.5%)
const DEFAULT_SLIPPAGE = 0.5;

// Transaction deadline (20 minutes)
const DEFAULT_DEADLINE_MINUTES = 20;

// Gas settings
const GAS_SETTINGS = {
    gasLimit: 300000,
    maxPriorityFeePerGas: '2000000000', // 2 gwei
    maxFeePerGas: '20000000000' // 20 gwei
};

// Export configuration
window.DexConfig = {
    RONIN_CHAIN_ID_MAINNET,
    RONIN_CHAIN_ID_SAIGON,
    TARGET_CHAIN_ID,
    TARGET_CHAIN_ID_HEX,
    RPC_URLS,
    CONTRACT_ADDRESSES,
    CURRENT_CONTRACTS,
    ROUTER_PRIORITY,
    FIREBASE_CONFIG,
    EXPLORER_URLS,
    NETWORK_NAMES,
    DEFAULT_SLIPPAGE,
    DEFAULT_DEADLINE_MINUTES,
    GAS_SETTINGS
};
