<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liquidity - The King's Dex</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-bg-deep-space text-text-primary pb-16">

    <header class="p-4 flex justify-between items-center sticky top-0 z-50 header-cyber">
        <a href="index.html" class="text-3xl app-title text-cyan-300 cursor-pointer">The King's Dex</a>
        <div id="wallet-section">
            <button id="connectWalletBtn" class="btn btn-connect-wallet text-sm">
                <i class="fas fa-wallet mr-2"></i>Connect Wallet
            </button>
            <div id="walletInfo" class="hidden wallet-address-display text-sm">
                <span id="walletAddress"></span>
            </div>
        </div>
    </header>

    <nav id="dexNav" class="bg-bg-element-dark shadow-md flex justify-center items-center space-x-2 md:space-x-4 border-b border-border-cyber">
        <a href="index.html" class="dex-nav-item" id="nav-landingPage">Home</a>
        <a href="swap.html" class="dex-nav-item" id="nav-swapPage">Swap</a>
        <a href="liquidity.html" class="dex-nav-item active" id="nav-poolsPage">Liquidity</a>
        <a href="farm.html" class="dex-nav-item" id="nav-farmPage">Farm</a>
        <a href="learn.html" class="dex-nav-item" id="nav-learnPage">Learn</a>
    </nav>

    <main class="container mx-auto px-4 py-8 page-content">
        <div class="max-w-md mx-auto space-y-6">
            <div class="card-cyber p-6 md:p-8 rounded-lg neon-glow-blue-soft">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="section-title text-2xl !mb-0 border-accent-cyan">Add Liquidity</h2>
                    <div class="flex space-x-2">
                        <button id="addLiquidityTab" class="px-3 py-1 rounded text-sm font-medium bg-purple-600 text-white">Add</button>
                        <button id="removeLiquidityTab" class="px-3 py-1 rounded text-sm font-medium text-gray-400 hover:text-white">Remove</button>
                    </div>
                </div>

                <!-- Add Liquidity Form -->
                <div id="addLiquidityForm">
                    <form id="liquidityForm" class="space-y-5">
                        <!-- Token A Section -->
                        <div class="form-group bg-bg-element-dark p-4 rounded-md border border-border-cyber-medium hover:border-accent-cyan transition-colors duration-300">
                            <div class="flex justify-between items-center mb-2">
                                <label class="form-label !mb-0">Token A</label>
                                <div class="text-xs text-text-secondary">Balance: <span id="tokenABalance" class="font-semibold">0.00</span></div>
                            </div>
                            <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
                                <input type="number" id="amountA" class="input-cyber flex-grow !py-3" placeholder="0.0" step="any">
                                <button type="button" id="tokenASelect" class="btn btn-cyber-secondary !px-3 !py-3 whitespace-nowrap w-full sm:w-40 flex items-center justify-center">
                                    <img id="tokenAIcon" src="https://placehold.co/24x24/181830/60EFFF?text=?" alt="Token" class="w-6 h-6 mr-2 rounded-full hidden">
                                    <span id="tokenASymbol">Select Token</span>
                                </button>
                            </div>
                            <button type="button" id="useMaxABtn" class="text-xs text-accent-blue hover:underline hover:text-accent-cyan mt-2 float-right transition-colors duration-200">Use MAX</button>
                        </div>

                        <!-- Plus Icon -->
                        <div class="text-center my-2">
                            <div class="p-3 rounded-full bg-bg-element-medium text-accent-cyan">
                                <i class="fas fa-plus text-xl"></i>
                            </div>
                        </div>

                        <!-- Token B Section -->
                        <div class="form-group bg-bg-element-dark p-4 rounded-md border border-border-cyber-medium hover:border-accent-cyan transition-colors duration-300">
                            <div class="flex justify-between items-center mb-2">
                                <label class="form-label !mb-0">Token B</label>
                                <div class="text-xs text-text-secondary">Balance: <span id="tokenBBalance" class="font-semibold">0.00</span></div>
                            </div>
                            <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
                                <input type="number" id="amountB" class="input-cyber flex-grow !py-3" placeholder="0.0" step="any">
                                <button type="button" id="tokenBSelect" class="btn btn-cyber-secondary !px-3 !py-3 whitespace-nowrap w-full sm:w-40 flex items-center justify-center">
                                    <img id="tokenBIcon" src="https://placehold.co/24x24/181830/60EFFF?text=?" alt="Token" class="w-6 h-6 mr-2 rounded-full hidden">
                                    <span id="tokenBSymbol">Select Token</span>
                                </button>
                            </div>
                            <button type="button" id="useMaxBBtn" class="text-xs text-accent-blue hover:underline hover:text-accent-cyan mt-2 float-right transition-colors duration-200">Use MAX</button>
                        </div>

                        <!-- Pool Info -->
                        <div id="poolInfoContainer" class="text-sm text-text-secondary space-y-1.5 pt-3 border-t border-border-cyber-medium hidden">
                            <div class="flex justify-between items-center">
                                <span>Pool Share:</span> 
                                <span id="poolShare" class="text-right text-text-primary">--</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span>LP Tokens:</span>
                                <span id="lpTokens" class="text-text-primary">--</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span>Exchange Rate:</span>
                                <span id="exchangeRate" class="text-text-primary">--</span>
                            </div>
                        </div>

                        <button type="submit" id="liquidityBtn" class="btn btn-cyber-primary w-full !py-3.5 text-lg mt-4" disabled>Connect Wallet</button>
                    </form>
                </div>

                <!-- Remove Liquidity Form -->
                <div id="removeLiquidityForm" class="hidden">
                    <div class="space-y-4">
                        <!-- LP Token Selection -->
                        <div class="form-group bg-bg-element-dark p-4 rounded-md border border-border-cyber-medium">
                            <div class="flex justify-between items-center mb-2">
                                <label class="form-label !mb-0">LP Token</label>
                                <div class="text-xs text-text-secondary">Balance: <span id="lpTokenBalance">0.00</span></div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <input type="number" id="lpAmount" class="input-cyber flex-grow !py-3" placeholder="0.0" step="any" min="0" max="100">
                                <span class="text-white font-medium">%</span>
                            </div>
                            <div class="flex justify-between items-center mt-2">
                                <div class="flex space-x-2">
                                    <button type="button" class="lp-percentage-btn text-xs bg-gray-600 hover:bg-gray-500 px-2 py-1 rounded" data-percentage="25">25%</button>
                                    <button type="button" class="lp-percentage-btn text-xs bg-gray-600 hover:bg-gray-500 px-2 py-1 rounded" data-percentage="50">50%</button>
                                    <button type="button" class="lp-percentage-btn text-xs bg-gray-600 hover:bg-gray-500 px-2 py-1 rounded" data-percentage="75">75%</button>
                                    <button type="button" class="lp-percentage-btn text-xs bg-gray-600 hover:bg-gray-500 px-2 py-1 rounded" data-percentage="100">MAX</button>
                                </div>
                            </div>
                        </div>

                        <!-- Removal Preview -->
                        <div id="removalPreview" class="bg-bg-element-dark p-4 rounded-md border border-border-cyber-medium hidden">
                            <h4 class="text-white font-medium mb-3">You will receive:</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center space-x-2">
                                        <img id="previewTokenAIcon" src="" alt="" class="w-5 h-5 rounded-full">
                                        <span id="previewTokenASymbol" class="text-white">-</span>
                                    </div>
                                    <span id="previewAmountA" class="text-white">0.00</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center space-x-2">
                                        <img id="previewTokenBIcon" src="" alt="" class="w-5 h-5 rounded-full">
                                        <span id="previewTokenBSymbol" class="text-white">-</span>
                                    </div>
                                    <span id="previewAmountB" class="text-white">0.00</span>
                                </div>
                            </div>
                        </div>

                        <button type="button" id="removeLiquidityBtn" class="btn btn-cyber-primary w-full !py-3.5 text-lg mt-4" disabled>Select LP Token</button>
                    </div>
                </div>
            </div>

            <!-- Protocol Information -->
            <div class="text-center space-y-2">
                <div class="text-xs text-text-secondary">
                    Automatically routes through the best available DEX for optimal liquidity provision.
                    <a href="learn.html#what-are-lp" class="text-accent-cyan hover:underline">Learn more about liquidity pools</a>.
                </div>
            </div>
        </div>
    </main>

    <div id="messageModal" class="modal-backdrop">
        <div class="modal-content">
             <h3 id="modalTitle" class="section-title text-xl mb-4">Notification</h3>
            <p id="modalMessageText" class="mb-4">Default message.</p>
            <p class="text-xs text-text-secondary mt-2 hidden" id="modalGasFeeContainer">
                Estimated Gas Fee: <span id="modalEstimatedGasFee" class="text-text-primary">-- RON</span>
            </p>
            <div id="modalActionSpinner" class="hidden my-3 animate-spin rounded-full h-8 w-8 border-b-2 border-accent-cyan mx-auto"></div>
            <div id="modalTransactionLinkContainer" class="hidden my-3 text-sm">
                <a href="#" id="modalTransactionLink" target="_blank" rel="noopener noreferrer" class="text-accent-blue hover:underline">View on Ronin Explorer</a>
            </div>
            <button id="closeModalBtn" class="btn btn-cyber-secondary px-6 py-2.5 text-sm mt-3">Close</button>
        </div>
    </div>
    
    <div id="tokenSelectModal" class="modal-backdrop">
        <div class="modal-content max-w-sm w-full">
            <h3 class="section-title text-xl mb-4 !border-accent-blue">Select a Token</h3>
            <input type="text" id="tokenSearchInput" class="input-cyber mb-4" placeholder="Search name or paste address">
            <div id="tokenListContainer" class="max-h-60 overflow-y-auto space-y-1 token-list-container">
                <p class="text-text-secondary p-4 text-center">Loading tokens...</p>
            </div>
            <button id="closeTokenSelectModalBtn" class="btn btn-cyber-secondary px-6 py-2.5 text-sm mt-4">Close</button>
        </div>
    </div>

    <!-- Enhanced Application Scripts -->
    <script src="js/config.js"></script>
    <script src="js/tokens.js"></script>
    <script src="js/wallet-provider.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/liquidity.js"></script>
    
    <!-- Frontend Integration -->
    <script type="module" src="main.js"></script>
</body>
</html>
