// Utility functions for The King's Dex

// Address utilities
function truncateAddress(address, startLength = 6, endLength = 4) {
    if (!address) return '';
    if (address.length <= startLength + endLength) return address;
    return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
}

function isValidAddress(address) {
    if (!address || typeof address !== 'string') return false;
    return /^0x[a-fA-F0-9]{40}$/.test(address);
}

function toChecksumAddress(address) {
    if (!window.web3 || !address) return address;
    try {
        return window.web3.utils.toChecksumAddress(address);
    } catch (error) {
        console.error('Error converting to checksum address:', error);
        return address;
    }
}

// Number formatting utilities
function formatNumber(number, decimals = 2) {
    if (isNaN(number)) return '0';
    
    const num = parseFloat(number);
    if (num === 0) return '0';
    
    // For very small numbers, use scientific notation
    if (num < 0.0001 && num > 0) {
        return num.toExponential(2);
    }
    
    // For large numbers, use compact notation
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    
    return num.toFixed(decimals);
}

function formatCurrency(amount, currency = 'USD', decimals = 2) {
    const num = parseFloat(amount);
    if (isNaN(num)) return '$0.00';
    
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(num);
}

function formatPercentage(value, decimals = 2) {
    const num = parseFloat(value);
    if (isNaN(num)) return '0%';
    return `${num.toFixed(decimals)}%`;
}

// Time utilities
function formatTimeAgo(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    return `${seconds} second${seconds > 1 ? 's' : ''} ago`;
}

function getDeadlineTimestamp(minutesFromNow = 20) {
    return Math.floor(Date.now() / 1000) + (minutesFromNow * 60);
}

// URL utilities
function getExplorerUrl(hash, type = 'tx') {
    const baseUrl = window.DexConfig.EXPLORER_URLS[window.DexConfig.TARGET_CHAIN_ID];
    return `${baseUrl}/${type}/${hash}`;
}

function openInExplorer(hash, type = 'tx') {
    const url = getExplorerUrl(hash, type);
    window.open(url, '_blank', 'noopener,noreferrer');
}

// Local storage utilities
function saveToStorage(key, value) {
    try {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
    } catch (error) {
        console.error('Error saving to storage:', error);
        return false;
    }
}

function loadFromStorage(key, defaultValue = null) {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.error('Error loading from storage:', error);
        return defaultValue;
    }
}

function removeFromStorage(key) {
    try {
        localStorage.removeItem(key);
        return true;
    } catch (error) {
        console.error('Error removing from storage:', error);
        return false;
    }
}

// Error handling utilities
function handleError(error, context = '') {
    console.error(`Error in ${context}:`, error);
    
    let message = 'An unexpected error occurred';
    
    if (error.message) {
        if (error.message.includes('User denied')) {
            message = 'Transaction was rejected by user';
        } else if (error.message.includes('insufficient funds')) {
            message = 'Insufficient funds for transaction';
        } else if (error.message.includes('execution reverted')) {
            message = 'Transaction failed - please check your inputs';
        } else {
            message = error.message;
        }
    }
    
    return message;
}

// Gas estimation utilities
async function estimateGas(transactionObject, options = {}) {
    try {
        if (!window.web3 || !window.walletProvider.isWalletConnected()) {
            throw new Error('Wallet not connected');
        }
        
        const gasLimit = await window.web3.eth.estimateGas({
            ...transactionObject,
            from: window.walletProvider.getCurrentAccount(),
            ...options
        });
        
        const gasPrice = await window.web3.eth.getGasPrice();
        const gasCost = window.web3.utils.fromWei(
            (BigInt(gasLimit) * BigInt(gasPrice)).toString(),
            'ether'
        );
        
        return {
            gasLimit: gasLimit.toString(),
            gasPrice: gasPrice.toString(),
            gasCost: parseFloat(gasCost).toFixed(6)
        };
    } catch (error) {
        console.error('Gas estimation failed:', error);
        throw error;
    }
}

// Slippage utilities
function calculateMinimumReceived(amount, slippagePercent) {
    const slippage = parseFloat(slippagePercent) / 100;
    const minReceived = parseFloat(amount) * (1 - slippage);
    return minReceived.toString();
}

function calculatePriceImpact(inputAmount, outputAmount, inputPrice, outputPrice) {
    try {
        const expectedOutput = parseFloat(inputAmount) * parseFloat(inputPrice) / parseFloat(outputPrice);
        const actualOutput = parseFloat(outputAmount);
        const impact = ((expectedOutput - actualOutput) / expectedOutput) * 100;
        return Math.max(0, impact);
    } catch (error) {
        console.error('Error calculating price impact:', error);
        return 0;
    }
}

// Validation utilities
function validateAmount(amount, balance, decimals = 18) {
    const errors = [];
    
    if (!amount || amount === '') {
        errors.push('Amount is required');
        return errors;
    }
    
    const numAmount = parseFloat(amount);
    
    if (isNaN(numAmount) || numAmount <= 0) {
        errors.push('Amount must be a positive number');
    }
    
    if (balance && parseFloat(balance) < numAmount) {
        errors.push('Insufficient balance');
    }
    
    // Check for too many decimal places
    const decimalPlaces = (amount.split('.')[1] || '').length;
    if (decimalPlaces > decimals) {
        errors.push(`Too many decimal places (max: ${decimals})`);
    }
    
    return errors;
}

function validateSlippage(slippage) {
    const num = parseFloat(slippage);
    
    if (isNaN(num) || num < 0.1 || num > 50) {
        return 'Slippage must be between 0.1% and 50%';
    }
    
    if (num > 5) {
        return 'High slippage warning: You may lose a significant amount due to price impact';
    }
    
    return null;
}

// Debounce utility
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Copy to clipboard utility
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (error) {
        console.error('Failed to copy to clipboard:', error);
        
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        
        try {
            document.execCommand('copy');
            document.body.removeChild(textArea);
            return true;
        } catch (fallbackError) {
            document.body.removeChild(textArea);
            return false;
        }
    }
}

// Export utilities
window.Utils = {
    // Address utilities
    truncateAddress,
    isValidAddress,
    toChecksumAddress,
    
    // Number formatting
    formatNumber,
    formatCurrency,
    formatPercentage,
    
    // Time utilities
    formatTimeAgo,
    getDeadlineTimestamp,
    
    // URL utilities
    getExplorerUrl,
    openInExplorer,
    
    // Storage utilities
    saveToStorage,
    loadFromStorage,
    removeFromStorage,
    
    // Error handling
    handleError,
    
    // Gas utilities
    estimateGas,
    
    // Slippage utilities
    calculateMinimumReceived,
    calculatePriceImpact,
    
    // Validation utilities
    validateAmount,
    validateSlippage,
    
    // Other utilities
    debounce,
    copyToClipboard
};
