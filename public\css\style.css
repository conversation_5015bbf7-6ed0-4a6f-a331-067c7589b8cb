/* Import Tailwind CSS */
@import url('https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css');

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Inter:wght@300;400;500;600&display=swap');

/* CSS Variables for consistent theming */
:root {
    --bg-deep-space: #080815; /* Deep, dark blue/purple, almost black */
    --bg-deep-space-rgb: 8, 8, 21;
    --bg-element-dark: #101022; /* Darker elements, card backgrounds */
    --bg-element-medium: #181830; /* Slightly lighter elements, modal backgrounds */
    --bg-element-light: #202040; /* Hover states, lighter accents */
    --border-cyber-medium-rgb: 42, 42, 90;

    --accent-cyan: #60EFFF;
    --accent-cyan-rgb: 96, 239, 255;
    --accent-magenta: #F85AFF;
    --accent-magenta-rgb: 248, 90, 255;
    --accent-blue: #5A78FF;
    --accent-blue-rgb: 90, 120, 255;
    --accent-green: #60FFB0;
    --accent-green-rgb: 96, 255, 176;
    --accent-yellow: #FFD700; /* For warnings, attention */
    --accent-yellow-rgb: 255, 215, 0;
    --accent-red: #FF4D4D; /* For errors, danger */
    --accent-red-rgb: 255, 77, 77;

    --text-primary: #E8E8FF; /* Light lavender/off-white for main text */
    --text-secondary: #A0A0CC; /* Lighter purple/grey for secondary text, placeholders */
    --text-placeholder: #7070A0; /* Darker placeholder text */
    --text-highlight: var(--accent-cyan); /* For emphasis */

    --border-cyber: #3A3A7A; /* Standard border for elements */
    --border-cyber-glow: #5A78FF; /* Border for elements with a subtle glow */
    --border-cyber-glow-rgb: 90, 120, 255;
    --border-cyber-medium: #2A2A5A; /* Darker border, subtle dividers */
    --border-active: var(--accent-cyan); /* For active tabs, inputs */

    --shadow-color-cyan: rgba(var(--accent-cyan-rgb), 0.4);
    --shadow-color-magenta: rgba(var(--accent-magenta-rgb), 0.4);
    --shadow-color-blue: rgba(var(--accent-blue-rgb), 0.4);
    --shadow-dark-strong: rgba(0,0,0,0.5);
    --shadow-dark-medium: rgba(0,0,0,0.3);

    --font-body: 'Inter', sans-serif;
    --font-heading: 'Orbitron', sans-serif;

    --border-radius-sm: 0.25rem; /* 4px */
    --border-radius-md: 0.375rem; /* 6px */
    --border-radius-lg: 0.5rem; /* 8px */
}

body {
    font-family: var(--font-body);
    background-color: var(--bg-deep-space);
    color: var(--text-primary);
    scroll-behavior: smooth;
    overflow-x: hidden;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-image:
        linear-gradient(rgba(var(--border-cyber-medium-rgb), 0.07) 1px, transparent 1px),
        linear-gradient(90deg, rgba(var(--border-cyber-medium-rgb), 0.07) 1px, transparent 1px);
    background-size: 22px 22px;
    line-height: 1.6;
}

main {
    flex-grow: 1;
}

/* Base Styles & Resets */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

h1, h2, h3, h4, .font-orbitron {
    font-family: var(--font-heading);
    letter-spacing: 0.5px;
    font-weight: 700;
}

a {
    color: var(--accent-cyan);
    text-decoration: none;
    transition: color 0.2s ease;
}
a:hover {
    color: var(--accent-magenta);
    text-decoration: underline;
}

/* Utility Classes */
.tooltip-icon {
    color: var(--text-secondary);
    cursor: help;
    transition: color 0.2s ease;
}
.tooltip-icon:hover {
    color: var(--accent-cyan);
}

/* Cyberpunk/Tech Theme Enhancements - Tailwind overrides */
.bg-gray-900 { background-color: var(--bg-deep-space) !important; }
.bg-gray-800 { background-color: var(--bg-element-dark) !important; }
.bg-gray-700 { background-color: var(--bg-element-medium) !important; }
.text-cyan-400 { color: var(--accent-cyan) !important; }
.text-cyan-300 { color: var(--accent-cyan) !important; }
.text-purple-400 { color: var(--accent-magenta) !important; }
.text-purple-300 { color: var(--accent-magenta) !important; }
.border-cyan-500 { border-color: var(--accent-cyan) !important; }
.focus\:ring-cyan-500:focus { --tw-ring-color: var(--accent-cyan); }
.focus\:border-cyan-500:focus { border-color: var(--accent-cyan); }

/* Custom background classes */
.bg-bg-deep-space { background-color: var(--bg-deep-space) !important; }
.bg-bg-element-dark { background-color: var(--bg-element-dark) !important; }
.bg-bg-element-medium { background-color: var(--bg-element-medium) !important; }
.bg-bg-element-light { background-color: var(--bg-element-light) !important; }

/* Custom text classes */
.text-text-primary { color: var(--text-primary) !important; }
.text-text-secondary { color: var(--text-secondary) !important; }
.text-text-placeholder { color: var(--text-placeholder) !important; }
.text-accent-cyan { color: var(--accent-cyan) !important; }
.text-accent-magenta { color: var(--accent-magenta) !important; }
.text-accent-blue { color: var(--accent-blue) !important; }
.text-accent-green { color: var(--accent-green) !important; }
.text-accent-yellow { color: var(--accent-yellow) !important; }
.text-accent-red { color: var(--accent-red) !important; }

/* Custom border classes */
.border-border-cyber { border-color: var(--border-cyber) !important; }
.border-border-cyber-glow { border-color: var(--border-cyber-glow) !important; }
.border-border-cyber-medium { border-color: var(--border-cyber-medium) !important; }
.border-accent-cyan { border-color: var(--accent-cyan) !important; }
.border-accent-magenta { border-color: var(--accent-magenta) !important; }
.border-accent-blue { border-color: var(--accent-blue) !important; }
.border-accent-green { border-color: var(--accent-green) !important; }

/* Header & Navigation */
.header-cyber {
    background-color: rgba(var(--bg-deep-space-rgb), 0.85);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid var(--border-cyber);
    box-shadow: 0 2px 20px var(--shadow-dark-medium);
}

.app-title {
    font-weight: 700;
    text-shadow: 0 0 8px var(--accent-cyan), 0 0 18px var(--accent-cyan), 0 0 30px var(--shadow-color-cyan);
    transition: text-shadow 0.3s ease;
}

.app-title:hover {
    text-shadow: 0 0 12px var(--accent-cyan), 0 0 24px var(--accent-cyan), 0 0 40px var(--shadow-color-cyan);
}

.nav-link {
    padding: 0.85rem 1.25rem;
    color: var(--text-secondary);
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.2s ease;
    border-bottom: 3px solid transparent;
    font-family: var(--font-heading);
    font-weight: 500;
    letter-spacing: 0.5px;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    width: 0;
    height: 3px;
    background-color: var(--accent-magenta);
    transition: all 0.3s ease-out;
    transform: translateX(-50%);
}

.nav-link:hover {
    color: var(--text-highlight);
    transform: translateY(-1px);
}

.nav-link:hover::after,
.nav-link.active-nav-link::after {
    width: 70%;
}

.nav-link.active-nav-link {
    color: var(--accent-cyan);
    font-weight: 600;
    border-bottom-color: var(--accent-cyan);
    text-shadow: 0 0 5px var(--shadow-color-cyan);
}

/* Mobile navigation improvements */
@media (max-width: 768px) {
    .nav-link {
        padding: 0.6rem 0.8rem;
        font-size: 0.9rem;
    }

    .app-title {
        font-size: 1.5rem !important;
    }
}

/* Neon Glow Effects */
.neon-glow-blue {
    box-shadow: 0 0 5px var(--accent-cyan), 0 0 10px var(--accent-cyan), 0 0 15px var(--accent-cyan), 0 0 20px var(--accent-cyan);
}
.neon-glow-blue-soft {
    filter: drop-shadow(0 0 8px rgba(var(--accent-cyan-rgb), 0.5));
}
.neon-glow-purple {
    box-shadow: 0 0 5px var(--accent-magenta), 0 0 10px var(--accent-magenta), 0 0 15px var(--accent-magenta);
}
.neon-glow-purple-soft {
    filter: drop-shadow(0 0 8px rgba(var(--accent-magenta-rgb), 0.7));
}
.neon-glow-green {
    box-shadow: 0 0 5px var(--accent-green), 0 0 10px var(--accent-green), 0 0 15px var(--accent-green);
}
.neon-glow-green-soft {
    filter: drop-shadow(0 0 8px rgba(var(--accent-green-rgb), 0.5));
}

/* Buttons */
.btn {
    font-family: var(--font-heading);
    font-weight: 500;
    letter-spacing: 1px;
    border-radius: var(--border-radius-md);
    transition: all 0.2s ease-out;
    text-transform: uppercase;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    border: 1px solid transparent;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.btn:disabled, .btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    filter: grayscale(60%);
    transform: none !important;
    box-shadow: none !important;
}

/* Page content visibility */
.page-content {
    display: block;
}

.page-content.hidden {
    display: none;
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(var(--bg-deep-space-rgb), 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.hidden {
    display: none;
}

/* Notifications */
.notification {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading spinner */
.loader {
    border-top-color: var(--accent-cyan);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: var(--bg-element-dark);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: var(--accent-cyan);
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--accent-magenta);
}

/* Slippage buttons */
.slippage-btn {
    background-color: var(--bg-element-medium);
    color: var(--text-secondary);
    border: 1px solid var(--border-cyber);
    transition: all 0.2s ease;
}

.slippage-btn:hover {
    background-color: var(--bg-element-light);
    color: var(--text-primary);
}

.slippage-btn.active {
    background-color: var(--accent-cyan);
    color: var(--bg-element-dark);
    border-color: var(--accent-cyan);
}

/* LP percentage buttons */
.lp-percentage-btn {
    background-color: var(--bg-element-medium);
    color: var(--text-secondary);
    border: 1px solid var(--border-cyber);
    transition: all 0.2s ease;
}

.lp-percentage-btn:hover {
    background-color: var(--bg-element-light);
    color: var(--text-primary);
}

.lp-percentage-btn.active {
    background-color: var(--accent-cyan);
    color: var(--bg-element-dark);
    border-color: var(--accent-cyan);
}
