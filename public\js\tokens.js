// Token definitions for The King's Dex
// Comprehensive token list for Ronin mainnet and testnet

// Chain IDs (duplicated here to avoid circular dependency)
const RONIN_CHAIN_ID_MAINNET = 2020;
const RONIN_CHAIN_ID_SAIGON = 2021;

// Token list for different networks
const TOKEN_LISTS = {
    [RONIN_CHAIN_ID_MAINNET]: [
        {
            symbol: 'R<PERSON>',
            name: '<PERSON><PERSON>',
            address: '******************************************',
            icon: 'https://cdn.axieinfinity.com/dapp-assets/tokens/ron.png',
            decimals: 18,
            isNative: true,
            isWhitelistedForSwap: true,
            isWhitelistedForLP: true
        },
        {
            symbol: 'AXS',
            name: 'Axie Infinity Shards',
            address: '0x97a9107c179342bdcfa5f8e6768efd527dfb58e4',
            icon: 'https://cdn.axieinfinity.com/dapp-assets/tokens/axs.png',
            decimals: 18,
            isWhitelistedForSwap: true,
            isWhitelistedForLP: true
        },
        {
            symbol: 'SLP',
            name: 'Smooth Love Potion',
            address: '0xa8754b9fa15fc18bb594588158ec0541a3adacd9',
            icon: 'https://cdn.axieinfinity.com/dapp-assets/tokens/slp.png',
            decimals: 0,
            isWhitelistedForSwap: true,
            isWhitelistedForLP: true
        },
        {
            symbol: 'USDC',
            name: 'USD Coin',
            address: '******************************************',
            icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/3408.png',
            decimals: 6,
            isWhitelistedForSwap: true,
            isWhitelistedForLP: true
        },
        {
            symbol: 'WETH',
            name: 'Wrapped Ethereum',
            address: '******************************************',
            icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1027.png',
            decimals: 18,
            isWhitelistedForSwap: true,
            isWhitelistedForLP: true
        },
        {
            symbol: 'PIXEL',
            name: 'Pixels',
            address: '******************************************',
            icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/28850.png',
            decimals: 18,
            isWhitelistedForSwap: true,
            isWhitelistedForLP: true
        }
    ],

    [RONIN_CHAIN_ID_SAIGON]: [
        {
            symbol: 'RON',
            name: 'Ronin',
            address: '******************************************',
            icon: 'https://cdn.axieinfinity.com/dapp-assets/tokens/ron.png',
            decimals: 18,
            isNative: true,
            isWhitelistedForSwap: true,
            isWhitelistedForLP: true
        },
        {
            symbol: 'AXS',
            name: 'Axie Infinity Shards',
            address: '******************************************',
            icon: 'https://cdn.axieinfinity.com/dapp-assets/tokens/axs.png',
            decimals: 18,
            isWhitelistedForSwap: true,
            isWhitelistedForLP: true
        },
        {
            symbol: 'SLP',
            name: 'Smooth Love Potion',
            address: '0xa8754b9fa15fc18bb594588158ec0541a3adacd9',
            icon: 'https://cdn.axieinfinity.com/dapp-assets/tokens/slp.png',
            decimals: 0,
            isWhitelistedForSwap: true,
            isWhitelistedForLP: true
        },
        {
            symbol: 'USDC',
            name: 'USD Coin',
            address: '******************************************',
            icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/3408.png',
            decimals: 6,
            isWhitelistedForSwap: true,
            isWhitelistedForLP: true
        },
        {
            symbol: 'WETH',
            name: 'Wrapped Ethereum',
            address: '******************************************',
            icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1027.png',
            decimals: 18,
            isWhitelistedForSwap: true,
            isWhitelistedForLP: true
        },
        {
            symbol: 'TEST1',
            name: 'Test Token 1',
            address: '******************************************',
            icon: 'https://placehold.co/64x64/F85AFF/FFFFFF?text=T1&font=orbitron',
            decimals: 18,
            isWhitelistedForSwap: true,
            isWhitelistedForLP: true
        },
        {
            symbol: 'TEST2',
            name: 'Test Token 2',
            address: '******************************************',
            icon: 'https://placehold.co/64x64/00FF00/FFFFFF?text=T2&font=orbitron',
            decimals: 18,
            isWhitelistedForSwap: true,
            isWhitelistedForLP: true
        }
    ]
};

// Get tokens for current network
function getCurrentNetworkTokens() {
    const targetChainId = window.DexConfig ? window.DexConfig.TARGET_CHAIN_ID : RONIN_CHAIN_ID_SAIGON;
    return TOKEN_LISTS[targetChainId] || [];
}

// Get token by address
function getTokenByAddress(address) {
    const tokens = getCurrentNetworkTokens();
    return tokens.find(token => 
        token.address.toLowerCase() === address.toLowerCase()
    );
}

// Get token by symbol
function getTokenBySymbol(symbol) {
    const tokens = getCurrentNetworkTokens();
    return tokens.find(token => 
        token.symbol.toLowerCase() === symbol.toLowerCase()
    );
}

// Filter tokens for swap (only whitelisted)
function getSwapTokens() {
    return getCurrentNetworkTokens().filter(token => token.isWhitelistedForSwap);
}

// Filter tokens for liquidity provision
function getLiquidityTokens() {
    return getCurrentNetworkTokens().filter(token => token.isWhitelistedForLP);
}

// Search tokens by name or symbol
function searchTokens(query) {
    const tokens = getCurrentNetworkTokens();
    const searchTerm = query.toLowerCase();
    
    return tokens.filter(token => 
        token.name.toLowerCase().includes(searchTerm) ||
        token.symbol.toLowerCase().includes(searchTerm) ||
        token.address.toLowerCase().includes(searchTerm)
    );
}

// Validate token address
function isValidTokenAddress(address) {
    if (!address || typeof address !== 'string') return false;
    
    // Check if it's a valid Ethereum address format
    const addressRegex = /^0x[a-fA-F0-9]{40}$/;
    if (!addressRegex.test(address)) return false;
    
    // Check if it's in our token list
    return !!getTokenByAddress(address);
}

// Get native token for current network
function getNativeToken() {
    const tokens = getCurrentNetworkTokens();
    return tokens.find(token => token.isNative);
}

// Format token amount for display
function formatTokenAmount(amount, decimals = 18, displayDecimals = 4) {
    if (!amount) return '0';
    
    try {
        const value = parseFloat(amount);
        if (value === 0) return '0';
        
        // For very small amounts, show more decimals
        if (value < 0.0001) {
            return value.toExponential(2);
        }
        
        return value.toFixed(displayDecimals);
    } catch (error) {
        console.error('Error formatting token amount:', error);
        return '0';
    }
}

// Convert token amount to wei (considering token decimals)
function toTokenWei(amount, decimals = 18) {
    if (!window.web3) {
        throw new Error('Web3 not initialized');
    }
    
    // Handle different decimal places
    if (decimals === 18) {
        return window.web3.utils.toWei(amount.toString(), 'ether');
    } else {
        // For non-18 decimal tokens, calculate manually
        const factor = Math.pow(10, decimals);
        return (parseFloat(amount) * factor).toString();
    }
}

// Convert wei to token amount (considering token decimals)
function fromTokenWei(amountWei, decimals = 18) {
    if (!window.web3) {
        throw new Error('Web3 not initialized');
    }
    
    if (decimals === 18) {
        return window.web3.utils.fromWei(amountWei.toString(), 'ether');
    } else {
        // For non-18 decimal tokens, calculate manually
        const factor = Math.pow(10, decimals);
        return (parseFloat(amountWei) / factor).toString();
    }
}

// Export token utilities
window.TokenUtils = {
    TOKEN_LISTS,
    getCurrentNetworkTokens,
    getTokenByAddress,
    getTokenBySymbol,
    getSwapTokens,
    getLiquidityTokens,
    searchTokens,
    isValidTokenAddress,
    getNativeToken,
    formatTokenAmount,
    toTokenWei,
    fromTokenWei
};
