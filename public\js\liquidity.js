// Liquidity functionality for The King's Dex

class LiquidityManager {
    constructor() {
        this.tokenA = null;
        this.tokenB = null;
        this.amountA = '';
        this.amountB = '';
        this.isInitialized = false;
        this.mode = 'add'; // 'add' or 'remove'
    }

    // Initialize liquidity interface
    initialize() {
        if (this.isInitialized) return;

        this.createLiquidityInterface();
        this.setupEventListeners();
        this.loadDefaultTokens();
        this.isInitialized = true;
    }

    // Create liquidity interface HTML
    createLiquidityInterface() {
        const container = document.getElementById('liquidity-component-container');
        if (!container) return;

        container.innerHTML = `
            <div class="max-w-md mx-auto bg-gray-800 rounded-xl shadow-xl border border-cyan-500 p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-white">Add Liquidity</h2>
                    <div class="flex space-x-2">
                        <button id="addLiquidityTab" class="px-3 py-1 rounded text-sm font-medium bg-purple-600 text-white">Add</button>
                        <button id="removeLiquidityTab" class="px-3 py-1 rounded text-sm font-medium text-gray-400 hover:text-white">Remove</button>
                    </div>
                </div>

                <!-- Add Liquidity Form -->
                <div id="addLiquidityForm">
                    <form id="liquidityForm" class="space-y-4">
                        <!-- Token A Section -->
                        <div class="bg-gray-700 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-2">
                                <label class="text-sm text-gray-300">Token A</label>
                                <span class="text-xs text-gray-400" id="tokenABalance">Balance: 0.00</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <input 
                                    type="number" 
                                    id="amountA" 
                                    placeholder="0.0" 
                                    class="flex-1 bg-transparent text-white text-xl font-semibold outline-none"
                                    step="any"
                                    min="0"
                                >
                                <button 
                                    type="button" 
                                    id="tokenASelect" 
                                    class="flex items-center space-x-2 bg-gray-600 hover:bg-gray-500 px-3 py-2 rounded-lg transition-colors"
                                >
                                    <img id="tokenAIcon" src="" alt="" class="w-6 h-6 rounded-full hidden">
                                    <span id="tokenASymbol" class="text-white font-medium">Select Token</span>
                                    <i class="fas fa-chevron-down text-gray-400"></i>
                                </button>
                            </div>
                            <div class="flex justify-between items-center mt-2">
                                <span class="text-xs text-gray-500" id="tokenAUSD">$0.00</span>
                                <button type="button" id="useMaxABtn" class="text-xs text-cyan-400 hover:text-cyan-300">MAX</button>
                            </div>
                        </div>

                        <!-- Plus Icon -->
                        <div class="flex justify-center">
                            <div class="bg-gray-700 p-2 rounded-full">
                                <i class="fas fa-plus text-cyan-400"></i>
                            </div>
                        </div>

                        <!-- Token B Section -->
                        <div class="bg-gray-700 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-2">
                                <label class="text-sm text-gray-300">Token B</label>
                                <span class="text-xs text-gray-400" id="tokenBBalance">Balance: 0.00</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <input 
                                    type="number" 
                                    id="amountB" 
                                    placeholder="0.0" 
                                    class="flex-1 bg-transparent text-white text-xl font-semibold outline-none"
                                    step="any"
                                    min="0"
                                >
                                <button 
                                    type="button" 
                                    id="tokenBSelect" 
                                    class="flex items-center space-x-2 bg-gray-600 hover:bg-gray-500 px-3 py-2 rounded-lg transition-colors"
                                >
                                    <img id="tokenBIcon" src="" alt="" class="w-6 h-6 rounded-full hidden">
                                    <span id="tokenBSymbol" class="text-white font-medium">Select Token</span>
                                    <i class="fas fa-chevron-down text-gray-400"></i>
                                </button>
                            </div>
                            <div class="flex justify-between items-center mt-2">
                                <span class="text-xs text-gray-500" id="tokenBUSD">$0.00</span>
                                <button type="button" id="useMaxBBtn" class="text-xs text-cyan-400 hover:text-cyan-300">MAX</button>
                            </div>
                        </div>

                        <!-- Pool Info -->
                        <div id="poolInfoContainer" class="bg-gray-700 rounded-lg p-4 hidden">
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-300">Pool Share</span>
                                    <span class="text-white" id="poolShare">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-300">LP Tokens</span>
                                    <span class="text-white" id="lpTokens">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-300">Exchange Rate</span>
                                    <span class="text-white" id="exchangeRate">-</span>
                                </div>
                            </div>
                        </div>

                        <!-- Add Liquidity Button -->
                        <button 
                            type="submit" 
                            id="liquidityBtn" 
                            class="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors"
                            disabled
                        >
                            Connect Wallet
                        </button>
                    </form>
                </div>

                <!-- Remove Liquidity Form -->
                <div id="removeLiquidityForm" class="hidden">
                    <div class="space-y-4">
                        <!-- LP Token Selection -->
                        <div class="bg-gray-700 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-2">
                                <label class="text-sm text-gray-300">LP Token</label>
                                <span class="text-xs text-gray-400" id="lpTokenBalance">Balance: 0.00</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <input 
                                    type="number" 
                                    id="lpAmount" 
                                    placeholder="0.0" 
                                    class="flex-1 bg-transparent text-white text-xl font-semibold outline-none"
                                    step="any"
                                    min="0"
                                    max="100"
                                >
                                <span class="text-white font-medium">%</span>
                            </div>
                            <div class="flex justify-between items-center mt-2">
                                <div class="flex space-x-2">
                                    <button type="button" class="lp-percentage-btn text-xs bg-gray-600 hover:bg-gray-500 px-2 py-1 rounded" data-percentage="25">25%</button>
                                    <button type="button" class="lp-percentage-btn text-xs bg-gray-600 hover:bg-gray-500 px-2 py-1 rounded" data-percentage="50">50%</button>
                                    <button type="button" class="lp-percentage-btn text-xs bg-gray-600 hover:bg-gray-500 px-2 py-1 rounded" data-percentage="75">75%</button>
                                    <button type="button" class="lp-percentage-btn text-xs bg-gray-600 hover:bg-gray-500 px-2 py-1 rounded" data-percentage="100">MAX</button>
                                </div>
                            </div>
                        </div>

                        <!-- Removal Preview -->
                        <div id="removalPreview" class="bg-gray-700 rounded-lg p-4 hidden">
                            <h4 class="text-white font-medium mb-3">You will receive:</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center space-x-2">
                                        <img id="previewTokenAIcon" src="" alt="" class="w-5 h-5 rounded-full">
                                        <span id="previewTokenASymbol" class="text-white">-</span>
                                    </div>
                                    <span id="previewAmountA" class="text-white">0.00</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center space-x-2">
                                        <img id="previewTokenBIcon" src="" alt="" class="w-5 h-5 rounded-full">
                                        <span id="previewTokenBSymbol" class="text-white">-</span>
                                    </div>
                                    <span id="previewAmountB" class="text-white">0.00</span>
                                </div>
                            </div>
                        </div>

                        <!-- Remove Liquidity Button -->
                        <button 
                            type="button" 
                            id="removeLiquidityBtn" 
                            class="w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors"
                            disabled
                        >
                            Select LP Token
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Setup event listeners
    setupEventListeners() {
        // Tab switching
        document.getElementById('addLiquidityTab')?.addEventListener('click', () => {
            this.switchMode('add');
        });

        document.getElementById('removeLiquidityTab')?.addEventListener('click', () => {
            this.switchMode('remove');
        });

        // Token selection buttons
        document.getElementById('tokenASelect')?.addEventListener('click', () => {
            this.openTokenSelectModal('tokenA');
        });

        document.getElementById('tokenBSelect')?.addEventListener('click', () => {
            this.openTokenSelectModal('tokenB');
        });

        // Amount inputs
        document.getElementById('amountA')?.addEventListener('input', (e) => {
            this.amountA = e.target.value;
            this.calculateAmountB();
            this.updateLiquidityButton();
        });

        document.getElementById('amountB')?.addEventListener('input', (e) => {
            this.amountB = e.target.value;
            this.calculateAmountA();
            this.updateLiquidityButton();
        });

        // Use max buttons
        document.getElementById('useMaxABtn')?.addEventListener('click', () => {
            this.useMaxAmount('A');
        });

        document.getElementById('useMaxBBtn')?.addEventListener('click', () => {
            this.useMaxAmount('B');
        });

        // LP percentage buttons
        document.querySelectorAll('.lp-percentage-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const percentage = btn.getAttribute('data-percentage');
                this.setLPPercentage(percentage);
            });
        });

        // LP amount input
        document.getElementById('lpAmount')?.addEventListener('input', (e) => {
            this.updateRemovalPreview(e.target.value);
        });

        // Form submissions
        document.getElementById('liquidityForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.addLiquidity();
        });

        document.getElementById('removeLiquidityBtn')?.addEventListener('click', () => {
            this.removeLiquidity();
        });

        // Listen for wallet connection changes
        if (window.walletProvider) {
            window.walletProvider.on('connect', () => {
                this.updateLiquidityButton();
                this.updateTokenBalances();
            });

            window.walletProvider.on('disconnect', () => {
                this.updateLiquidityButton();
                this.clearTokenBalances();
            });
        }
    }

    // Switch between add and remove modes
    switchMode(mode) {
        this.mode = mode;

        const addTab = document.getElementById('addLiquidityTab');
        const removeTab = document.getElementById('removeLiquidityTab');
        const addForm = document.getElementById('addLiquidityForm');
        const removeForm = document.getElementById('removeLiquidityForm');

        if (mode === 'add') {
            addTab?.classList.add('bg-purple-600', 'text-white');
            addTab?.classList.remove('text-gray-400');
            removeTab?.classList.remove('bg-purple-600', 'text-white');
            removeTab?.classList.add('text-gray-400');
            
            addForm?.classList.remove('hidden');
            removeForm?.classList.add('hidden');
        } else {
            removeTab?.classList.add('bg-purple-600', 'text-white');
            removeTab?.classList.remove('text-gray-400');
            addTab?.classList.remove('bg-purple-600', 'text-white');
            addTab?.classList.add('text-gray-400');
            
            removeForm?.classList.remove('hidden');
            addForm?.classList.add('hidden');
        }

        this.updateLiquidityButton();
    }

    // Load default tokens
    loadDefaultTokens() {
        const tokens = window.TokenUtils.getLiquidityTokens();
        if (tokens.length >= 2) {
            // Set RON as default token A
            const ronToken = tokens.find(t => t.isNative);
            if (ronToken) {
                this.selectToken('tokenA', ronToken);
            }

            // Set USDC as default token B if available
            const usdcToken = tokens.find(t => t.symbol === 'USDC');
            if (usdcToken) {
                this.selectToken('tokenB', usdcToken);
            }
        }
    }

    // Open token selection modal
    openTokenSelectModal(type) {
        const excludeToken = type === 'tokenA' ? this.tokenB : this.tokenA;
        
        window.uiManager.openModal('tokenSelect', {
            context: type,
            excludeToken: excludeToken,
            onSelect: (token) => {
                this.selectToken(type, token);
            }
        });
    }

    // Select a token
    selectToken(type, token) {
        if (type === 'tokenA') {
            this.tokenA = token;
            this.updateTokenUI('tokenA', token);
        } else {
            this.tokenB = token;
            this.updateTokenUI('tokenB', token);
        }

        this.updateTokenBalances();
        this.updatePoolInfo();
        this.updateLiquidityButton();
    }

    // Update token UI
    updateTokenUI(type, token) {
        const symbolEl = document.getElementById(`${type}Symbol`);
        const iconEl = document.getElementById(`${type}Icon`);

        if (symbolEl) symbolEl.textContent = token.symbol;
        if (iconEl) {
            iconEl.src = token.icon;
            iconEl.alt = token.symbol;
            iconEl.classList.remove('hidden');
        }
    }

    // Calculate amount B based on amount A
    calculateAmountB() {
        if (!this.tokenA || !this.tokenB || !this.amountA) return;

        // This would typically fetch the current pool ratio
        // For now, we'll use a 1:1 ratio as simulation
        const ratio = 1; // Replace with actual pool ratio
        this.amountB = (parseFloat(this.amountA) * ratio).toString();

        const amountBEl = document.getElementById('amountB');
        if (amountBEl) amountBEl.value = window.Utils.formatNumber(this.amountB, 6);
    }

    // Calculate amount A based on amount B
    calculateAmountA() {
        if (!this.tokenA || !this.tokenB || !this.amountB) return;

        // This would typically fetch the current pool ratio
        // For now, we'll use a 1:1 ratio as simulation
        const ratio = 1; // Replace with actual pool ratio
        this.amountA = (parseFloat(this.amountB) / ratio).toString();

        const amountAEl = document.getElementById('amountA');
        if (amountAEl) amountAEl.value = window.Utils.formatNumber(this.amountA, 6);
    }

    // Use maximum amount for a token
    async useMaxAmount(tokenType) {
        const token = tokenType === 'A' ? this.tokenA : this.tokenB;
        if (!token || !window.walletProvider.isWalletConnected()) return;

        try {
            const balance = await window.walletProvider.getTokenBalance(token.address);
            
            // Reserve some RON for gas if using native token
            let maxAmount = parseFloat(balance);
            if (token.isNative && maxAmount > 0.01) {
                maxAmount -= 0.01; // Reserve 0.01 RON for gas
            }

            if (tokenType === 'A') {
                this.amountA = maxAmount.toString();
                const amountAEl = document.getElementById('amountA');
                if (amountAEl) amountAEl.value = this.amountA;
                this.calculateAmountB();
            } else {
                this.amountB = maxAmount.toString();
                const amountBEl = document.getElementById('amountB');
                if (amountBEl) amountBEl.value = this.amountB;
                this.calculateAmountA();
            }

            this.updateLiquidityButton();
        } catch (error) {
            console.error('Error getting max amount:', error);
        }
    }

    // Update pool information
    updatePoolInfo() {
        if (!this.tokenA || !this.tokenB) return;

        // This would fetch actual pool data
        // For now, we'll simulate the data
        const poolInfo = {
            poolShare: '0.01%',
            lpTokens: '0.0001',
            exchangeRate: `1 ${this.tokenA.symbol} = 1 ${this.tokenB.symbol}`
        };

        const container = document.getElementById('poolInfoContainer');
        const poolShareEl = document.getElementById('poolShare');
        const lpTokensEl = document.getElementById('lpTokens');
        const exchangeRateEl = document.getElementById('exchangeRate');

        if (poolShareEl) poolShareEl.textContent = poolInfo.poolShare;
        if (lpTokensEl) lpTokensEl.textContent = poolInfo.lpTokens;
        if (exchangeRateEl) exchangeRateEl.textContent = poolInfo.exchangeRate;

        if (container) container.classList.remove('hidden');
    }

    // Update token balances
    async updateTokenBalances() {
        if (!window.walletProvider.isWalletConnected()) return;

        try {
            if (this.tokenA) {
                const balance = await window.walletProvider.getTokenBalance(this.tokenA.address);
                const balanceEl = document.getElementById('tokenABalance');
                if (balanceEl) {
                    balanceEl.textContent = `Balance: ${window.Utils.formatNumber(balance, 4)}`;
                }
            }

            if (this.tokenB) {
                const balance = await window.walletProvider.getTokenBalance(this.tokenB.address);
                const balanceEl = document.getElementById('tokenBBalance');
                if (balanceEl) {
                    balanceEl.textContent = `Balance: ${window.Utils.formatNumber(balance, 4)}`;
                }
            }
        } catch (error) {
            console.error('Error updating token balances:', error);
        }
    }

    // Clear token balances
    clearTokenBalances() {
        const tokenABalanceEl = document.getElementById('tokenABalance');
        const tokenBBalanceEl = document.getElementById('tokenBBalance');

        if (tokenABalanceEl) tokenABalanceEl.textContent = 'Balance: 0.00';
        if (tokenBBalanceEl) tokenBBalanceEl.textContent = 'Balance: 0.00';
    }

    // Set LP percentage for removal
    setLPPercentage(percentage) {
        const lpAmountEl = document.getElementById('lpAmount');
        if (lpAmountEl) {
            lpAmountEl.value = percentage;
            this.updateRemovalPreview(percentage);
        }
    }

    // Update removal preview
    updateRemovalPreview(percentage) {
        if (!this.tokenA || !this.tokenB || !percentage) return;

        // This would calculate actual amounts based on LP token balance
        // For now, we'll simulate the calculation
        const simulatedAmountA = (parseFloat(percentage) / 100) * 10; // Simulate 10 tokens
        const simulatedAmountB = (parseFloat(percentage) / 100) * 10; // Simulate 10 tokens

        const container = document.getElementById('removalPreview');
        const previewAmountA = document.getElementById('previewAmountA');
        const previewAmountB = document.getElementById('previewAmountB');
        const previewTokenASymbol = document.getElementById('previewTokenASymbol');
        const previewTokenBSymbol = document.getElementById('previewTokenBSymbol');
        const previewTokenAIcon = document.getElementById('previewTokenAIcon');
        const previewTokenBIcon = document.getElementById('previewTokenBIcon');

        if (previewAmountA) previewAmountA.textContent = window.Utils.formatNumber(simulatedAmountA, 6);
        if (previewAmountB) previewAmountB.textContent = window.Utils.formatNumber(simulatedAmountB, 6);
        if (previewTokenASymbol) previewTokenASymbol.textContent = this.tokenA.symbol;
        if (previewTokenBSymbol) previewTokenBSymbol.textContent = this.tokenB.symbol;
        if (previewTokenAIcon) previewTokenAIcon.src = this.tokenA.icon;
        if (previewTokenBIcon) previewTokenBIcon.src = this.tokenB.icon;

        if (container) container.classList.remove('hidden');

        // Update remove button
        const removeBtn = document.getElementById('removeLiquidityBtn');
        if (removeBtn) {
            removeBtn.textContent = `Remove ${percentage}% Liquidity`;
            removeBtn.disabled = false;
        }
    }

    // Update liquidity button state
    updateLiquidityButton() {
        const liquidityBtn = document.getElementById('liquidityBtn');
        const removeLiquidityBtn = document.getElementById('removeLiquidityBtn');

        if (this.mode === 'add') {
            if (!liquidityBtn) return;

            if (!window.walletProvider.isWalletConnected()) {
                liquidityBtn.textContent = 'Connect Wallet';
                liquidityBtn.disabled = false;
                return;
            }

            if (!this.tokenA || !this.tokenB) {
                liquidityBtn.textContent = 'Select tokens';
                liquidityBtn.disabled = true;
                return;
            }

            if (!this.amountA || !this.amountB || parseFloat(this.amountA) <= 0 || parseFloat(this.amountB) <= 0) {
                liquidityBtn.textContent = 'Enter amounts';
                liquidityBtn.disabled = true;
                return;
            }

            liquidityBtn.textContent = 'Add Liquidity';
            liquidityBtn.disabled = false;
        } else {
            if (!removeLiquidityBtn) return;

            if (!window.walletProvider.isWalletConnected()) {
                removeLiquidityBtn.textContent = 'Connect Wallet';
                removeLiquidityBtn.disabled = false;
                return;
            }

            if (!this.tokenA || !this.tokenB) {
                removeLiquidityBtn.textContent = 'Select LP Token';
                removeLiquidityBtn.disabled = true;
                return;
            }

            // Button state is updated in updateRemovalPreview
        }
    }

    // Add liquidity
    async addLiquidity() {
        if (!window.walletProvider.isWalletConnected()) {
            await window.uiManager.connectWallet();
            return;
        }

        if (!this.tokenA || !this.tokenB || !this.amountA || !this.amountB) {
            window.uiManager.showNotification('Liquidity Error', 'Please complete all fields', 'error');
            return;
        }

        try {
            window.uiManager.showLoadingModal('Adding Liquidity', 'Preparing transaction...');

            // This would integrate with your actual DEX contracts
            // For now, we'll simulate the process
            await this.simulateAddLiquidity();

            window.uiManager.closeModal('loading');
            window.uiManager.showNotification(
                'Liquidity Added',
                `Added ${this.amountA} ${this.tokenA.symbol} and ${this.amountB} ${this.tokenB.symbol} to the pool`,
                'success'
            );

            // Reset form
            this.resetLiquidityForm();

        } catch (error) {
            window.uiManager.closeModal('loading');
            window.uiManager.showNotification('Add Liquidity Failed', window.Utils.handleError(error), 'error');
        }
    }

    // Remove liquidity
    async removeLiquidity() {
        if (!window.walletProvider.isWalletConnected()) {
            await window.uiManager.connectWallet();
            return;
        }

        const lpAmount = document.getElementById('lpAmount')?.value;
        if (!lpAmount || parseFloat(lpAmount) <= 0) {
            window.uiManager.showNotification('Removal Error', 'Please enter a valid percentage', 'error');
            return;
        }

        try {
            window.uiManager.showLoadingModal('Removing Liquidity', 'Preparing transaction...');

            // This would integrate with your actual DEX contracts
            await this.simulateRemoveLiquidity();

            window.uiManager.closeModal('loading');
            window.uiManager.showNotification(
                'Liquidity Removed',
                `Removed ${lpAmount}% of your liquidity position`,
                'success'
            );

            // Reset form
            this.resetRemovalForm();

        } catch (error) {
            window.uiManager.closeModal('loading');
            window.uiManager.showNotification('Remove Liquidity Failed', window.Utils.handleError(error), 'error');
        }
    }

    // Simulate add liquidity (replace with actual contract interaction)
    async simulateAddLiquidity() {
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('Simulated add liquidity:', {
            tokenA: this.tokenA,
            tokenB: this.tokenB,
            amountA: this.amountA,
            amountB: this.amountB
        });
    }

    // Simulate remove liquidity (replace with actual contract interaction)
    async simulateRemoveLiquidity() {
        await new Promise(resolve => setTimeout(resolve, 2000));
        const lpAmount = document.getElementById('lpAmount')?.value;
        console.log('Simulated remove liquidity:', {
            tokenA: this.tokenA,
            tokenB: this.tokenB,
            percentage: lpAmount
        });
    }

    // Reset liquidity form
    resetLiquidityForm() {
        this.amountA = '';
        this.amountB = '';

        const amountAEl = document.getElementById('amountA');
        const amountBEl = document.getElementById('amountB');
        const poolInfoContainer = document.getElementById('poolInfoContainer');

        if (amountAEl) amountAEl.value = '';
        if (amountBEl) amountBEl.value = '';
        if (poolInfoContainer) poolInfoContainer.classList.add('hidden');

        this.updateLiquidityButton();
        this.updateTokenBalances();
    }

    // Reset removal form
    resetRemovalForm() {
        const lpAmountEl = document.getElementById('lpAmount');
        const removalPreview = document.getElementById('removalPreview');
        const removeLiquidityBtn = document.getElementById('removeLiquidityBtn');

        if (lpAmountEl) lpAmountEl.value = '';
        if (removalPreview) removalPreview.classList.add('hidden');
        if (removeLiquidityBtn) {
            removeLiquidityBtn.textContent = 'Select LP Token';
            removeLiquidityBtn.disabled = true;
        }
    }
}

// Initialize liquidity manager
window.LiquidityManager = new LiquidityManager();
