# 🚀 Uniswap V2 Deployment Guide for Ronin Testnet

## ✅ Status: Ready to Deploy!

Your Uniswap V2 contracts have been successfully compiled and are ready for deployment to Ronin Testnet.

## 📋 Pre-Deployment Checklist

- ✅ Contracts compiled successfully
- ✅ Hardhat configuration ready
- ✅ Deployment scripts created
- ✅ Address update scripts ready
- ⏳ Environment variables needed
- ⏳ Testnet RON needed for gas

## 🔧 Setup Instructions

### 1. Create Environment File

Create a `.env` file in the root directory:

```bash
# Copy the example file
cp .env.example .env
```

Then edit `.env` with your values:

```env
# Your wallet private key (without 0x prefix)
DEPLOYER_PRIVATE_KEY=your_private_key_here

# Network selection
NETWORK=saigon

# RPC URLs (default values should work)
RONIN_RPC_URL=https://api.roninchain.com/rpc
RONIN_SAIGON_RPC_URL=https://saigon-testnet.roninchain.com/rpc
```

### 2. Get Testnet RON

You'll need some testnet RON for gas fees:

1. **Get a Ronin Wallet** if you don't have one
2. **Switch to Saigon Testnet** in your wallet
3. **Get testnet RON** from the Ronin faucet:
   - Visit: https://faucet.roninchain.com/
   - Connect your wallet
   - Request testnet RON

### 3. Deploy Contracts

Once you have testnet RON and your `.env` file set up:

```bash
# Deploy to Ronin Testnet (Saigon)
npm run deploy:saigon
```

This will:
- Deploy UniswapV2Factory
- Deploy UniswapV2Router02
- Verify the deployments
- Save deployment info to `deployments/uniswap-v2-saigon.json`

### 4. Update Configuration

After successful deployment, update your configuration files:

```bash
# This will automatically update all config files with the new addresses
npm run update-config:saigon
```

Or manually:
```bash
node scripts/update-contract-addresses.js saigon
```

### 5. Deploy Cloud Functions

Update and deploy your Firebase functions:

```bash
cd functions
npm run deploy
```

## 📊 Expected Output

When you run the deployment, you should see:

```
🚀 Starting Uniswap V2 deployment on Ronin...
📋 Deployment Details:
  Network: ronin_saigon (Chain ID: 2021)
  Deployer: 0xYourAddress
  Balance: X.XX RON
  WRON Address: 0xA959726154953bAe111746E265E6d754F48570E6

📦 Deploying UniswapV2Factory...
✅ UniswapV2Factory deployed:
  Address: 0xFactoryAddress
  Transaction: 0xTxHash

📦 Deploying UniswapV2Router02...
✅ UniswapV2Router02 deployed:
  Address: 0xRouterAddress
  Transaction: 0xTxHash

🔍 Verifying deployments...
✅ All verifications passed!

💾 Deployment info saved to: deployments/uniswap-v2-saigon.json

🎉 Deployment Summary:
==================================================
Network: ronin_saigon (2021)
Factory: 0xFactoryAddress
Router: 0xRouterAddress
WRON: 0xA959726154953bAe111746E265E6d754F48570E6
==================================================

✅ Deployment completed successfully!
```

## 🔍 Verification Steps

After deployment, verify everything works:

1. **Check contract addresses** in the deployment file
2. **Verify contracts** are deployed on Ronin explorer
3. **Test the integration** with your frontend
4. **Create a test pool** to verify functionality

## 🚨 Security Notes

- **Never commit your `.env` file** to version control
- **Use a dedicated deployment wallet** for security
- **Test thoroughly** on testnet before mainnet
- **Keep your private keys secure**

## 🐛 Troubleshooting

### Common Issues:

**"Insufficient balance"**
- Get more testnet RON from the faucet

**"Network connection failed"**
- Check your RPC URL in `.env`
- Try a different RPC endpoint

**"Private key invalid"**
- Ensure private key is without `0x` prefix
- Check for extra spaces or characters

**"Contract deployment failed"**
- Check gas limits in hardhat.config.js
- Verify network configuration

## 📞 Next Steps After Deployment

1. **Test the contracts** with small amounts
2. **Create initial liquidity pools** for testing
3. **Update frontend** to use new contract addresses
4. **Test the multi-protocol integration**
5. **Deploy to mainnet** when ready

## 🎯 Ready to Deploy?

Run this command when you're ready:

```bash
npm run deploy:saigon
```

The contracts are compiled and ready to go! 🚀
