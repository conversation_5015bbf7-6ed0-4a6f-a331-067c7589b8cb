/* Import Tailwind CSS */
@import url('https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css');

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Inter:wght@300;400;500;600&display=swap');

/* CSS Variables for consistent theming */
:root {
    --bg-deep-space: #080815; /* Deep, dark blue/purple, almost black */
    --bg-deep-space-rgb: 8, 8, 21;
    --bg-element-dark: #101022; /* Darker elements, card backgrounds */
    --bg-element-medium: #181830; /* Slightly lighter elements, modal backgrounds */
    --bg-element-light: #202040; /* Hover states, lighter accents */
    --border-cyber-medium-rgb: 42, 42, 90;

    --accent-cyan: #60EFFF;
    --accent-cyan-rgb: 96, 239, 255;
    --accent-magenta: #F85AFF;
    --accent-magenta-rgb: 248, 90, 255;
    --accent-blue: #5A78FF;
    --accent-blue-rgb: 90, 120, 255;
    --accent-green: #60FFB0;
    --accent-green-rgb: 96, 255, 176;
    --accent-yellow: #FFD700; /* For warnings, attention */
    --accent-yellow-rgb: 255, 215, 0;
    --accent-red: #FF4D4D; /* For errors, danger */
    --accent-red-rgb: 255, 77, 77;

    --text-primary: #E8E8FF; /* Light lavender/off-white for main text */
    --text-secondary: #A0A0CC; /* Lighter purple/grey for secondary text, placeholders */
    --text-placeholder: #7070A0; /* Darker placeholder text */
    --text-highlight: var(--accent-cyan); /* For emphasis */

    --border-cyber: #3A3A7A; /* Standard border for elements */
    --border-cyber-glow: #5A78FF; /* Border for elements with a subtle glow */
    --border-cyber-glow-rgb: 90, 120, 255;
    --border-cyber-medium: #2A2A5A; /* Darker border, subtle dividers */
    --border-active: var(--accent-cyan); /* For active tabs, inputs */

    --shadow-color-cyan: rgba(var(--accent-cyan-rgb), 0.4);
    --shadow-color-magenta: rgba(var(--accent-magenta-rgb), 0.4);
    --shadow-color-blue: rgba(var(--accent-blue-rgb), 0.4);
    --shadow-dark-strong: rgba(0,0,0,0.5);
    --shadow-dark-medium: rgba(0,0,0,0.3);

    --font-body: 'Inter', sans-serif;
    --font-heading: 'Orbitron', sans-serif;

    --border-radius-sm: 0.25rem; /* 4px */
    --border-radius-md: 0.375rem; /* 6px */
    --border-radius-lg: 0.5rem; /* 8px */
}

body {
    font-family: var(--font-body);
    background-color: var(--bg-deep-space);
    color: var(--text-primary);
    scroll-behavior: smooth;
    overflow-x: hidden;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-image:
        linear-gradient(rgba(var(--border-cyber-medium-rgb), 0.07) 1px, transparent 1px),
        linear-gradient(90deg, rgba(var(--border-cyber-medium-rgb), 0.07) 1px, transparent 1px);
    background-size: 22px 22px;
    line-height: 1.6;
}

main {
    flex-grow: 1;
}

/* Base Styles & Resets */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

h1, h2, h3, h4, .font-orbitron {
    font-family: var(--font-heading);
    letter-spacing: 0.5px;
    font-weight: 700;
}

a {
    color: var(--accent-cyan);
    text-decoration: none;
    transition: color 0.2s ease;
}
a:hover {
    color: var(--accent-magenta);
    text-decoration: underline;
}

/* Utility Classes */
.tooltip-icon {
    color: var(--text-secondary);
    cursor: help;
    transition: color 0.2s ease;
}
.tooltip-icon:hover {
    color: var(--accent-cyan);
}

/* Cyberpunk/Tech Theme Enhancements - Tailwind overrides */
.bg-gray-900 { background-color: var(--bg-deep-space) !important; }
.bg-gray-800 { background-color: var(--bg-element-dark) !important; }
.bg-gray-700 { background-color: var(--bg-element-medium) !important; }
.text-cyan-400 { color: var(--accent-cyan) !important; }
.text-cyan-300 { color: var(--accent-cyan) !important; }
.text-purple-400 { color: var(--accent-magenta) !important; }
.text-purple-300 { color: var(--accent-magenta) !important; }
.border-cyan-500 { border-color: var(--accent-cyan) !important; }
.focus\:ring-cyan-500:focus { --tw-ring-color: var(--accent-cyan); }
.focus\:border-cyan-500:focus { border-color: var(--accent-cyan); }

/* Custom background classes */
.bg-bg-deep-space { background-color: var(--bg-deep-space) !important; }
.bg-bg-element-dark { background-color: var(--bg-element-dark) !important; }
.bg-bg-element-medium { background-color: var(--bg-element-medium) !important; }
.bg-bg-element-light { background-color: var(--bg-element-light) !important; }

/* Custom text classes */
.text-text-primary { color: var(--text-primary) !important; }
.text-text-secondary { color: var(--text-secondary) !important; }
.text-text-placeholder { color: var(--text-placeholder) !important; }
.text-accent-cyan { color: var(--accent-cyan) !important; }
.text-accent-magenta { color: var(--accent-magenta) !important; }
.text-accent-blue { color: var(--accent-blue) !important; }
.text-accent-green { color: var(--accent-green) !important; }
.text-accent-yellow { color: var(--accent-yellow) !important; }
.text-accent-red { color: var(--accent-red) !important; }

/* Custom border classes */
.border-border-cyber { border-color: var(--border-cyber) !important; }
.border-border-cyber-glow { border-color: var(--border-cyber-glow) !important; }
.border-border-cyber-medium { border-color: var(--border-cyber-medium) !important; }
.border-accent-cyan { border-color: var(--accent-cyan) !important; }
.border-accent-magenta { border-color: var(--accent-magenta) !important; }
.border-accent-blue { border-color: var(--accent-blue) !important; }
.border-accent-green { border-color: var(--accent-green) !important; }


/* Header & Navigation */
.header-cyber {
    background-color: rgba(var(--bg-deep-space-rgb), 0.85);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid var(--border-cyber);
    box-shadow: 0 2px 20px var(--shadow-dark-medium);
}

.app-title {
    font-weight: 700;
    text-shadow: 0 0 8px var(--accent-cyan), 0 0 18px var(--accent-cyan), 0 0 30px var(--shadow-color-cyan);
    transition: text-shadow 0.3s ease;
}

.app-title:hover {
    text-shadow: 0 0 12px var(--accent-cyan), 0 0 24px var(--accent-cyan), 0 0 40px var(--shadow-color-cyan);
}

.dex-nav-item {
    padding: 0.85rem 1.25rem;
    color: var(--text-secondary);
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.2s ease;
    border-bottom: 3px solid transparent;
    font-family: var(--font-heading);
    font-weight: 500;
    letter-spacing: 0.5px;
    position: relative;
}

.dex-nav-item::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    width: 0;
    height: 3px;
    background-color: var(--accent-magenta);
    transition: all 0.3s ease-out;
    transform: translateX(-50%);
}

.dex-nav-item:hover {
    color: var(--text-highlight);
    transform: translateY(-1px);
}

.dex-nav-item:hover::after,
.dex-nav-item.active::after {
    width: 70%;
}

.dex-nav-item.active {
    color: var(--accent-cyan);
    font-weight: 600;
    border-bottom-color: var(--accent-cyan);
    text-shadow: 0 0 5px var(--shadow-color-cyan);
}

/* Mobile navigation improvements */
@media (max-width: 768px) {
    .dex-nav-item {
        padding: 0.6rem 0.8rem;
        font-size: 0.9rem;
    }

    .app-title {
        font-size: 1.5rem !important;
    }
}


/* Neon Glow Effects */
.neon-glow-blue {
    box-shadow: 0 0 5px var(--accent-cyan), 0 0 10px var(--accent-cyan), 0 0 15px var(--accent-cyan), 0 0 20px var(--accent-cyan);
}
.neon-glow-blue-soft {
    filter: drop-shadow(0 0 8px rgba(var(--accent-cyan-rgb), 0.5));
}
.neon-glow-purple {
    box-shadow: 0 0 5px var(--accent-magenta), 0 0 10px var(--accent-magenta), 0 0 15px var(--accent-magenta);
}
.neon-glow-purple-soft {
    filter: drop-shadow(0 0 8px rgba(var(--accent-magenta-rgb), 0.7));
}
.neon-glow-green {
    box-shadow: 0 0 5px var(--accent-green), 0 0 10px var(--accent-green), 0 0 15px var(--accent-green);
}
.neon-glow-green-soft {
    filter: drop-shadow(0 0 8px rgba(var(--accent-green-rgb), 0.5));
}

/* Buttons */
.btn {
    font-family: var(--font-heading);
    font-weight: 500;
    letter-spacing: 1px;
    border-radius: var(--border-radius-md);
    transition: all 0.2s ease-out;
    text-transform: uppercase;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    border: 1px solid transparent;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.btn:disabled, .btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    filter: grayscale(60%);
    transform: none !important;
    box-shadow: none !important;
}

.btn-cyber-primary {
    background: linear-gradient(135deg, var(--accent-blue), var(--accent-magenta));
    color: white;
    border-color: var(--accent-magenta);
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    box-shadow: 0 2px 8px rgba(var(--accent-magenta-rgb), 0.3), 0 0 15px rgba(var(--accent-blue-rgb), 0.2), inset 0 1px 0 rgba(255,255,255,0.1);
}

.btn-cyber-primary:hover:not(:disabled) {
    box-shadow: 0 4px 12px rgba(var(--accent-magenta-rgb), 0.5), 0 0 25px rgba(var(--accent-blue-rgb), 0.35), inset 0 1px 0 rgba(255,255,255,0.15);
    filter: brightness(1.15);
    transform: translateY(-2px);
}

.btn-cyber-secondary {
    background-color: transparent;
    color: var(--accent-cyan);
    border: 1px solid var(--accent-cyan);
    box-shadow: inset 0 0 6px rgba(var(--accent-cyan-rgb),0.2);
}

.btn-cyber-secondary:hover:not(:disabled) {
    background-color: rgba(var(--accent-cyan-rgb), 0.1);
    color: white;
    border-color: var(--accent-cyan);
    box-shadow: 0 0 10px var(--shadow-color-cyan), inset 0 0 8px rgba(var(--accent-cyan-rgb),0.15);
    transform: translateY(-1px);
}

.btn-connect-wallet, .wallet-address-display {
    background-color: var(--bg-element-medium);
    color: var(--accent-cyan);
    border: 1px solid var(--border-cyber-glow);
    padding: 0.6rem 1rem;
    border-radius: var(--border-radius-md);
    box-shadow: 0 0 8px var(--shadow-color-blue);
    font-family: var(--font-heading);
    font-weight: 500;
}

.btn-connect-wallet:hover:not(:disabled) {
    background-color: var(--bg-element-dark);
    border-color: var(--accent-cyan);
    box-shadow: 0 0 15px var(--shadow-color-cyan);
    color: white;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    letter-spacing: 0.8px;
}

.btn.slippage-option-btn.active {
    background-color: var(--accent-cyan) !important;
    color: var(--bg-element-dark) !important;
    border-color: var(--accent-cyan) !important;
    font-weight: bold;
}


/* Input Fields & Forms */
.input-cyber {
    background-color: var(--bg-element-medium);
    border: 1px solid var(--border-cyber);
    color: var(--text-primary);
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.3), 0 0 8px rgba(var(--accent-blue-rgb), 0.1);
    transition: all 0.3s ease;
    border-radius: var(--border-radius-md);
    padding: 0.8rem 1rem;
    width: 100%;
    font-size: 0.95rem;
}

.input-cyber::placeholder {
    color: var(--text-placeholder);
}

.input-cyber:focus, .input-cyber:focus-within {
    border-color: var(--border-active);
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.3), 0 0 15px rgba(var(--accent-blue-rgb), 0.5);
    background-color: var(--bg-element-dark);
    outline: none;
}

.input-cyber[readonly] {
    background-color: var(--bg-element-dark);
    opacity: 0.6;
    cursor: not-allowed;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--accent-cyan);
    font-weight: 500;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group {
    margin-bottom: 1.5rem;
}

/* Legacy input support */
input[type="text"], input[type="number"] {
    background-color: var(--bg-element-medium);
    border: 1px solid var(--border-cyber);
    color: var(--text-primary);
    border-radius: var(--border-radius-md);
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

input[type="text"]:focus, input[type="number"]:focus {
    outline: none;
    border-color: var(--accent-cyan);
    box-shadow: 0 0 0 2px rgba(var(--accent-cyan-rgb), 0.5);
}

input::placeholder {
    color: var(--text-placeholder);
}

/* Section Titles */
.section-title {
    font-weight: 700;
    text-shadow: 0 0 8px var(--shadow-color-magenta);
    padding-bottom: 0.6rem;
    border-bottom: 2px solid var(--accent-magenta);
    display: inline-block;
    margin-bottom: 1.25rem;
}

/* Modifiers for section titles with different accent colors */
.section-title.border-accent-cyan { border-bottom-color: var(--accent-cyan); text-shadow: 0 0 8px var(--shadow-color-cyan); }
.section-title.border-accent-blue { border-bottom-color: var(--accent-blue); text-shadow: 0 0 8px var(--shadow-color-blue); }
.section-title.border-accent-green { border-bottom-color: var(--accent-green); text-shadow: 0 0 8px rgba(var(--accent-green-rgb),0.5); }

/* Cards */
.card-cyber {
    background: linear-gradient(155deg, var(--bg-element-medium) 0%, var(--bg-element-dark) 100%);
    border: 1px solid var(--border-cyber-medium);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 6px 18px var(--shadow-dark-medium), 0 0 10px rgba(var(--border-cyber-glow-rgb), 0.1), inset 0 0 2px rgba(var(--accent-cyan-rgb),0.05);
    transition: transform 0.3s ease-out, box-shadow 0.3s ease-out, border-color 0.3s ease-out;
    overflow: hidden;
}

.card-cyber:hover {
    transform: translateY(-4px) scale(1.015);
    border-color: var(--border-cyber-glow);
    box-shadow: 0 10px 25px var(--shadow-dark-strong), 0 0 20px var(--shadow-color-blue), inset 0 0 4px rgba(var(--accent-cyan-rgb),0.1);
}

/* Modals */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(var(--bg-deep-space-rgb), 0.85);
    backdrop-filter: blur(6px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.25s ease-in-out, visibility 0.25s ease-in-out;
}

.modal-backdrop.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-element-medium);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-cyber-glow);
    box-shadow: 0 0 35px var(--shadow-color-blue), 0 0 10px var(--shadow-color-cyan) inset;
    color: var(--text-primary);
    width: 90%;
    max-width: 420px;
    text-align: center;
    transform: scale(0.9) translateY(20px);
    transition: transform 0.25s ease-out, opacity 0.25s ease-out;
    opacity: 0;
}

.modal-backdrop.active .modal-content {
    transform: scale(1) translateY(0);
    opacity: 1;
}

.modal-content p {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-secondary);
}

.modal-content #modalMessageText {
    color: var(--text-primary);
}

.modal-content .btn {
    margin-top: 1.5rem;
}

/* Legacy modal support */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(var(--bg-deep-space-rgb), 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.hidden {
    display: none;
}

@keyframes modal-fade-in {
    from { opacity: 0; transform: translateY(-20px) scale(0.95); }
    to { opacity: 1; transform: translateY(0) scale(1); }
}

/* Token List in Modal */
.token-list-container {
    scrollbar-width: thin;
    scrollbar-color: var(--accent-blue) var(--bg-element-dark);
}

.token-list-container::-webkit-scrollbar {
    width: 6px;
}

.token-list-container::-webkit-scrollbar-track {
    background: var(--bg-element-dark);
    border-radius: 3px;
}

.token-list-container::-webkit-scrollbar-thumb {
    background: var(--accent-blue);
    border-radius: 3px;
}

.token-list-container::-webkit-scrollbar-thumb:hover {
    background: var(--accent-cyan);
}

.token-select-item {
    transition: background-color 0.2s ease;
}

.token-select-item:hover {
    background-color: rgba(var(--accent-cyan-rgb), 0.1) !important;
}

.token-select-item img {
    border: 1px solid var(--border-cyber-medium);
    box-shadow: 0 0 4px rgba(var(--accent-cyan-rgb), 0.3);
}

/* Custom Scrollbar for Token List (legacy) */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: var(--bg-element-dark);
    border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: var(--border-cyber);
    border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--accent-cyan);
}

/* Info Banners (for NXS burn, warnings, etc.) */
.info-banner {
    font-size: 0.875rem;
    padding: 0.85rem 1rem;
    border-radius: var(--border-radius-md);
    border: 1px solid;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    line-height: 1.5;
}

.info-banner.warning {
    color: var(--accent-yellow);
    background-color: rgba(var(--accent-yellow-rgb), 0.08);
    border-color: rgba(var(--accent-yellow-rgb), 0.3);
    text-shadow: 0 0 2px rgba(var(--accent-yellow-rgb),0.2);
}

.info-banner.info {
    color: var(--accent-cyan);
    background-color: rgba(var(--accent-cyan-rgb), 0.08);
    border-color: rgba(var(--accent-cyan-rgb), 0.3);
}

.info-banner.error {
    color: var(--accent-red);
    background-color: rgba(var(--accent-red-rgb), 0.08);
    border-color: rgba(var(--accent-red-rgb), 0.3);
}

.info-banner i {
    margin-top: 0.125rem;
}

/* Page transition placeholder */
.page-content {
    animation: page-fade-in 0.5s ease-out;
}

@keyframes page-fade-in {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Loader animation */
.loader {
    border-top-color: var(--accent-cyan);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Loading Spinner (used in messageModal and other places) */
.animate-spin {
    border-color: var(--border-cyber);
    border-bottom-color: var(--accent-cyan);
}

/* Learn Page Specifics */
.learn-category-link {
    font-family: var(--font-heading);
    font-weight: 500;
    border-left: 3px solid transparent;
    padding-left: 0.75rem;
}

.learn-category-link.active,
.learn-category-link:hover {
    background-color: var(--bg-element-light) !important;
    color: var(--accent-cyan) !important;
    border-left-color: var(--accent-cyan);
}

.learn-article {
    display: none;
}

.learn-article.active {
    display: block;
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* General Tags */
.tag-cyber {
    background-color: rgba(var(--accent-blue-rgb), 0.15);
    color: var(--accent-blue);
    border: 1px solid rgba(var(--accent-blue-rgb), 0.4);
    padding: 0.2rem 0.5rem;
    font-weight: 500;
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    display: inline-block;
}

/* Swap Component Specifics */
.swap-input-group {
    background-color: var(--bg-element-dark);
    border: 1px solid var(--border-cyber-medium);
    border-radius: var(--border-radius-lg);
    padding: 1rem;
    margin-bottom: 1rem;
}

.swap-input-group:focus-within {
    border-color: var(--accent-cyan);
    box-shadow: 0 0 0 2px rgba(var(--accent-cyan-rgb), 0.3);
}

.token-select-button {
    background-color: var(--bg-element-medium);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-md);
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    font-size: 0.875rem;
}

.token-select-button:hover {
    background-color: var(--bg-element-light);
}

.token-select-button img {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    border-radius: 50%;
}

.swap-arrow-button {
    background-color: var(--bg-element-medium);
    border: 1px solid var(--border-cyber);
    color: var(--accent-cyan);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0.5rem auto;
    transition: all 0.2s ease;
}

.swap-arrow-button:hover {
    background-color: var(--bg-element-light);
    transform: rotate(180deg);
    border-color: var(--accent-cyan);
}

.slippage-btn {
    background-color: var(--bg-element-medium);
    color: var(--text-secondary);
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius-md);
    font-size: 0.75rem;
}

.slippage-btn:hover, .slippage-btn.active {
    background-color: var(--accent-magenta);
    color: white;
}

/* Shadow for cards/containers */
.card-shadow {
    box-shadow: 0px 0px 15px 0px rgba(var(--accent-cyan-rgb), 0.1), 0px 0px 5px 0px rgba(var(--accent-cyan-rgb), 0.05);
}

.shadow-inner-top {
    box-shadow: inset 0 4px 6px -1px rgba(0,0,0,0.2);
}

/* Specific component enhancements from HTML pages */
#swapPriceInfo, #minReceivedInfo, #priceImpactInfo, #lpFeeInfo, #swapEstimatedGasFee,
#addLiqEstimatedGasFee, #removeLiqEstimatedGasFee, #stakeModalEstGasFee {
    font-weight: 500;
}

/* Ensure tooltip icons are vertically aligned nicely if next to text */
.form-label i.tooltip-icon, .flex span i.tooltip-icon {
    vertical-align: middle;
}

/* Ensure Orbitron font is applied if Tailwind overrides */
body, button, input, select, textarea, .font-orbitron {
    font-family: var(--font-body) !important;
}

h1, h2, h3, h4, h5, h6, .font-orbitron {
    font-family: var(--font-heading) !important;
}

/* Enhanced Responsive Design */
@media (max-width: 640px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .card-cyber {
        margin: 0.5rem 0;
    }

    .btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .modal-content {
        width: 95%;
        padding: 1.5rem;
    }

    .input-cyber {
        padding: 0.7rem 0.8rem;
    }
}

@media (max-width: 480px) {
    .app-title {
        font-size: 1.25rem !important;
    }

    .dex-nav-item {
        padding: 0.5rem 0.6rem;
        font-size: 0.8rem;
    }

    .btn {
        padding: 0.5rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus improvements for accessibility */
.btn:focus-visible,
.input-cyber:focus-visible,
.dex-nav-item:focus-visible {
    outline: 2px solid var(--accent-cyan);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --text-primary: #FFFFFF;
        --text-secondary: #E0E0E0;
        --border-cyber: #FFFFFF;
        --border-cyber-glow: #FFFFFF;
    }
}

/* Additional animations and effects */
@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--accent-cyan), 0 0 10px var(--accent-cyan), 0 0 15px var(--accent-cyan);
    }
    50% {
        box-shadow: 0 0 10px var(--accent-cyan), 0 0 20px var(--accent-cyan), 0 0 30px var(--accent-cyan);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
}

.float-animation {
    animation: float 3s ease-in-out infinite;
}

/* Improved hover states */
.card-cyber:hover {
    animation: pulse-glow 2s ease-in-out infinite;
}

/* Loading states */
.loading-shimmer {
    background: linear-gradient(90deg, var(--bg-element-dark) 25%, var(--bg-element-medium) 50%, var(--bg-element-dark) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Success and error states */
.success-glow {
    box-shadow: 0 0 10px var(--accent-green), 0 0 20px var(--accent-green);
    border-color: var(--accent-green) !important;
}

.error-glow {
    box-shadow: 0 0 10px var(--accent-red), 0 0 20px var(--accent-red);
    border-color: var(--accent-red) !important;
}

/* Improved button interactions */
.btn:active {
    transform: scale(0.98);
}

.btn-cyber-primary:active {
    transform: scale(0.98) translateY(-1px);
}

/* Enhanced form validation states */
.input-cyber.valid {
    border-color: var(--accent-green);
    box-shadow: 0 0 0 2px rgba(var(--accent-green-rgb), 0.3);
}

.input-cyber.invalid {
    border-color: var(--accent-red);
    box-shadow: 0 0 0 2px rgba(var(--accent-red-rgb), 0.3);
}

/* Tooltip improvements */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-element-dark);
    color: var(--text-primary);
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    border: 1px solid var(--border-cyber);
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}
