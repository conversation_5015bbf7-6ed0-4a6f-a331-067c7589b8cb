// scripts/verify-setup.js
// Verification script to check if everything is ready for deployment

const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🔍 Verifying deployment setup...\n');

let allGood = true;
const issues = [];
const warnings = [];

// Check if .env file exists
if (!fs.existsSync('.env')) {
    issues.push('❌ .env file not found. Copy .env.example to .env and fill in your values.');
    allGood = false;
} else {
    console.log('✅ .env file found');
    
    // Check required environment variables
    const requiredVars = ['DEPLOYER_PRIVATE_KEY', 'NETWORK'];
    for (const varName of requiredVars) {
        if (!process.env[varName]) {
            issues.push(`❌ Missing environment variable: ${varName}`);
            allGood = false;
        } else {
            console.log(`✅ ${varName} is set`);
        }
    }
    
    // Check private key format
    if (process.env.DEPLOYER_PRIVATE_KEY) {
        if (process.env.DEPLOYER_PRIVATE_KEY.startsWith('0x')) {
            warnings.push('⚠️  Private key should not include 0x prefix');
        }
        if (process.env.DEPLOYER_PRIVATE_KEY.length !== 64) {
            issues.push('❌ Private key should be 64 characters long (without 0x)');
            allGood = false;
        }
    }
    
    // Check network setting
    if (process.env.NETWORK && !['mainnet', 'saigon'].includes(process.env.NETWORK)) {
        issues.push('❌ NETWORK should be either "mainnet" or "saigon"');
        allGood = false;
    }
}

// Check if contracts are compiled
const artifactsDir = path.join(__dirname, '..', 'artifacts', 'contracts');
if (!fs.existsSync(artifactsDir)) {
    issues.push('❌ Contracts not compiled. Run: npx hardhat compile');
    allGood = false;
} else {
    console.log('✅ Contracts compiled');
    
    // Check specific contract artifacts
    const requiredContracts = ['UniswapV2Factory.sol', 'UniswapV2Router02.sol'];
    for (const contract of requiredContracts) {
        const contractPath = path.join(artifactsDir, contract);
        if (!fs.existsSync(contractPath)) {
            issues.push(`❌ Missing contract artifact: ${contract}`);
            allGood = false;
        } else {
            console.log(`✅ ${contract} compiled`);
        }
    }
}

// Check if hardhat.config.js exists
if (!fs.existsSync('hardhat.config.js')) {
    issues.push('❌ hardhat.config.js not found');
    allGood = false;
} else {
    console.log('✅ Hardhat configuration found');
}

// Check if deployment script exists
const deployScript = path.join(__dirname, 'deploy-hardhat.js');
if (!fs.existsSync(deployScript)) {
    issues.push('❌ Deployment script not found');
    allGood = false;
} else {
    console.log('✅ Deployment script found');
}

// Check if node_modules exists
if (!fs.existsSync('node_modules')) {
    issues.push('❌ Dependencies not installed. Run: npm install');
    allGood = false;
} else {
    console.log('✅ Dependencies installed');
}

// Check if deployments directory exists (create if not)
const deploymentsDir = path.join(__dirname, '..', 'deployments');
if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir, { recursive: true });
    console.log('✅ Created deployments directory');
} else {
    console.log('✅ Deployments directory exists');
}

console.log('\n' + '='.repeat(50));

// Display warnings
if (warnings.length > 0) {
    console.log('\n⚠️  Warnings:');
    warnings.forEach(warning => console.log(warning));
}

// Display issues
if (issues.length > 0) {
    console.log('\n❌ Issues found:');
    issues.forEach(issue => console.log(issue));
}

// Final status
if (allGood && warnings.length === 0) {
    console.log('\n🎉 Everything looks good! Ready to deploy!');
    console.log('\nTo deploy to Ronin Testnet, run:');
    console.log('npm run deploy:saigon');
} else if (allGood) {
    console.log('\n✅ Setup is ready, but please review the warnings above.');
    console.log('\nTo deploy to Ronin Testnet, run:');
    console.log('npm run deploy:saigon');
} else {
    console.log('\n❌ Please fix the issues above before deploying.');
}

console.log('\n' + '='.repeat(50));

// Exit with appropriate code
process.exit(allGood ? 0 : 1);
