// functions/config/contractAddresses.js
// Contract addresses configuration - Update these after deployment

const { RONIN_CHAIN_ID_MAINNET, RONIN_CHAIN_ID_SAIGON } = require('../utils/contracts');

// Current target network
const CURRENT_NETWORK = process.env.NETWORK || 'mainnet';
const CURRENT_CHAIN_ID = CURRENT_NETWORK === 'mainnet' ? RONIN_CHAIN_ID_MAINNET : RONIN_CHAIN_ID_SAIGON;

// Contract addresses by network
const CONTRACT_ADDRESSES = {
    [RONIN_CHAIN_ID_MAINNET]: {
        // Uniswap V2 contracts (to be updated after deployment)
        UNISWAP_V2_FACTORY: process.env.UNISWAP_V2_FACTORY_MAINNET || '0xYOUR_UNISWAP_V2_FACTORY_MAINNET_ADDRESS',
        UNISWAP_V2_ROUTER: process.env.UNISWAP_V2_ROUTER_MAINNET || '0xYOUR_UNISWAP_V2_ROUTER_MAINNET_ADDRESS',
        
        // Katana DEX contracts (existing)
        KATANA_FACTORY: process.env.KATANA_FACTORY_MAINNET || '0x7d0556d55ca1a92708681e2e231733ebd922597d',
        KATANA_ROUTER: process.env.KATANA_ROUTER_MAINNET || '0x7d0556d55ca1a92708681e2e231733ebd922597d',
        
        // Token addresses
        WRON: '0xc99a6a985ed2cac1ef41640596c5a5f9f4e19ef5',
        NXS: process.env.NXS_TOKEN_MAINNET || '0xYOUR_NXS_MAINNET_ADDRESS',
        
        // Common tokens on Ronin mainnet
        AXS: '0x97a9107c179342bdcfa5f8e6768efd527dfb58e4',
        SLP: '0xa8754b9fa15fc18bb594588158ec0541a3adacd9',
        USDC: '0x0b7007c13325c48911f73a2dad5fa5dcbf808adc'
    },
    
    [RONIN_CHAIN_ID_SAIGON]: {
        // Uniswap V2 contracts (deployed to Saigon testnet)
        UNISWAP_V2_FACTORY: process.env.UNISWAP_V2_FACTORY_SAIGON || '0x3b34882989cF524B062470De68D1F4C053C9E98B',
        UNISWAP_V2_ROUTER: process.env.UNISWAP_V2_ROUTER_SAIGON || '0x64edE7475b34511F124Db708941A7767aeeD9a07',
        
        // Katana DEX contracts (not available on testnet)
        KATANA_FACTORY: null, // Katana not deployed on Saigon testnet
        KATANA_ROUTER: null,   // Katana not deployed on Saigon testnet
        
        // Token addresses (testnet)
        WRON: '0xA959726154953bAe111746E265E6d754F48570E6',
        NXS: process.env.NXS_TOKEN_SAIGON || null, // NXS token not yet deployed on testnet

        // Test tokens (to be deployed if needed)
        TEST_TOKEN_A: null,
        TEST_TOKEN_B: null
    }
};

// Get addresses for current network
const CURRENT_ADDRESSES = CONTRACT_ADDRESSES[CURRENT_CHAIN_ID];

// Validation function to check if addresses are properly set
function validateAddresses() {
    const requiredAddresses = ['UNISWAP_V2_FACTORY', 'UNISWAP_V2_ROUTER', 'WRON'];
    const missingAddresses = [];
    
    for (const address of requiredAddresses) {
        if (!CURRENT_ADDRESSES[address] || CURRENT_ADDRESSES[address].includes('YOUR_')) {
            missingAddresses.push(address);
        }
    }
    
    return {
        isValid: missingAddresses.length === 0,
        missingAddresses
    };
}

// Helper function to get address with validation
function getAddress(contractName) {
    const address = CURRENT_ADDRESSES[contractName];
    if (!address || address.includes('YOUR_') || address.includes('ADDRESS')) {
        console.warn(`Address for ${contractName} not properly configured on ${CURRENT_NETWORK}`);
        return null;
    }
    return address;
}

// Helper function to check if a contract is deployed
function isContractDeployed(contractName) {
    const address = getAddress(contractName);
    return address !== null;
}

// Export configuration
module.exports = {
    CURRENT_NETWORK,
    CURRENT_CHAIN_ID,
    CONTRACT_ADDRESSES,
    CURRENT_ADDRESSES,
    
    // Helper functions
    validateAddresses,
    getAddress,
    isContractDeployed,
    
    // Specific getters for commonly used addresses
    getUniswapV2Factory: () => getAddress('UNISWAP_V2_FACTORY'),
    getUniswapV2Router: () => getAddress('UNISWAP_V2_ROUTER'),
    getKatanaFactory: () => getAddress('KATANA_FACTORY'),
    getKatanaRouter: () => getAddress('KATANA_ROUTER'),
    getWRON: () => getAddress('WRON'),
    getNXS: () => getAddress('NXS')
};
