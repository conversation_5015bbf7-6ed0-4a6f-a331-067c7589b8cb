// Main application initialization for The King's Dex

class KingsDexApp {
    constructor() {
        this.isInitialized = false;
        this.currentPage = 'swap';
    }

    // Initialize the application
    async initialize() {
        if (this.isInitialized) return;

        console.log("King's Dex Initializing...");

        try {
            // Initialize Firebase if available
            this.initializeFirebase();

            // Initialize wallet provider
            await this.initializeWallet();

            // Initialize UI components
            this.initializeUI();

            // Set up global event listeners
            this.setupGlobalEventListeners();

            // Initialize the current page
            this.initializeCurrentPage();

            // Auto-connect wallet if previously connected
            await this.autoConnectWallet();

            this.isInitialized = true;
            console.log("King's Dex initialized successfully");

        } catch (error) {
            console.error('Error initializing application:', error);
            this.showInitializationError(error);
        }
    }

    // Initialize Firebase
    initializeFirebase() {
        try {
            if (typeof firebase !== 'undefined' && window.DexConfig.FIREBASE_CONFIG) {
                if (!firebase.apps.length) {
                    firebase.initializeApp(window.DexConfig.FIREBASE_CONFIG);
                    console.log('Firebase initialized');
                }
            } else {
                console.warn('Firebase not available or not configured');
            }
        } catch (error) {
            console.error('Error initializing Firebase:', error);
        }
    }

    // Initialize wallet provider
    async initializeWallet() {
        try {
            if (window.walletProvider) {
                const initialized = await window.walletProvider.initialize();
                if (initialized) {
                    console.log('Wallet provider initialized');
                } else {
                    console.warn('Wallet provider not available');
                }
            }
        } catch (error) {
            console.error('Error initializing wallet provider:', error);
        }
    }

    // Initialize UI components
    initializeUI() {
        try {
            // UI Manager should already be initialized
            if (window.uiManager) {
                console.log('UI Manager initialized');
            }

            // Initialize page managers
            if (window.SwapManager) {
                console.log('Swap Manager available');
            }

            if (window.LiquidityManager) {
                console.log('Liquidity Manager available');
            }

            // Set up modal close buttons
            this.setupModalCloseButtons();

            // Update current year in footer
            this.updateFooter();

        } catch (error) {
            console.error('Error initializing UI:', error);
        }
    }

    // Setup global event listeners
    setupGlobalEventListeners() {
        // Handle window resize
        window.addEventListener('resize', this.handleWindowResize.bind(this));

        // Handle page visibility changes
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));

        // Handle beforeunload for cleanup
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));

        // Handle hash changes for navigation
        window.addEventListener('hashchange', this.handleHashChange.bind(this));

        // Handle keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
    }

    // Setup modal close buttons
    setupModalCloseButtons() {
        // Token select modal
        const closeTokenSelectBtn = document.getElementById('closeTokenSelectModalBtn');
        if (closeTokenSelectBtn) {
            closeTokenSelectBtn.addEventListener('click', () => {
                window.uiManager.closeModal('tokenSelect');
            });
        }

        // Info modal
        const closeInfoModalBtn = document.getElementById('closeInfoModalBtn');
        const infoModalOkBtn = document.getElementById('infoModalOkBtn');
        
        if (closeInfoModalBtn) {
            closeInfoModalBtn.addEventListener('click', () => {
                window.uiManager.closeModal('info');
            });
        }

        if (infoModalOkBtn) {
            infoModalOkBtn.addEventListener('click', () => {
                window.uiManager.closeModal('info');
            });
        }

        // Slippage modal
        const closeSlippageModalBtn = document.getElementById('closeSlippageModalBtn');
        if (closeSlippageModalBtn) {
            closeSlippageModalBtn.addEventListener('click', () => {
                window.uiManager.closeModal('slippage');
            });
        }
    }

    // Initialize current page based on URL hash
    initializeCurrentPage() {
        const hash = window.location.hash.substring(1);
        const validPages = ['swap', 'liquidity', 'farm', 'learn'];
        
        if (validPages.includes(hash)) {
            this.currentPage = hash;
        } else {
            this.currentPage = 'swap';
            window.location.hash = '#swap';
        }

        // Navigate to the current page
        if (window.uiManager) {
            window.uiManager.navigateToPage(this.currentPage);
        }
    }

    // Auto-connect wallet if previously connected
    async autoConnectWallet() {
        try {
            if (window.uiManager) {
                await window.uiManager.autoConnectWallet();
            }
        } catch (error) {
            console.error('Auto-connect failed:', error);
        }
    }

    // Handle window resize
    handleWindowResize() {
        // Update mobile menu visibility if needed
        const mobileMenu = document.getElementById('mobileMenu');
        if (mobileMenu && window.innerWidth >= 768) {
            mobileMenu.classList.add('hidden');
        }
    }

    // Handle page visibility changes
    handleVisibilityChange() {
        if (document.hidden) {
            // Page is hidden - pause any timers or polling
            console.log('Page hidden - pausing updates');
        } else {
            // Page is visible - resume updates
            console.log('Page visible - resuming updates');
            
            // Refresh data if wallet is connected
            if (window.walletProvider && window.walletProvider.isWalletConnected()) {
                this.refreshPageData();
            }
        }
    }

    // Handle before unload
    handleBeforeUnload() {
        // Clean up any pending operations
        console.log('Page unloading - cleaning up');
    }

    // Handle hash changes for navigation
    handleHashChange() {
        const hash = window.location.hash.substring(1);
        const validPages = ['swap', 'liquidity', 'farm', 'learn'];
        
        if (validPages.includes(hash) && hash !== this.currentPage) {
            this.currentPage = hash;
            if (window.uiManager) {
                window.uiManager.navigateToPage(this.currentPage);
            }
        }
    }

    // Handle keyboard shortcuts
    handleKeyboardShortcuts(event) {
        // Escape key to close modals
        if (event.key === 'Escape') {
            if (window.uiManager) {
                window.uiManager.closeAllModals();
            }
        }

        // Ctrl/Cmd + K to focus search (if implemented)
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            const searchInput = document.getElementById('tokenSearchInput');
            if (searchInput && !searchInput.closest('.modal').classList.contains('hidden')) {
                searchInput.focus();
            }
        }
    }

    // Refresh page data
    refreshPageData() {
        try {
            switch (this.currentPage) {
                case 'swap':
                    if (window.SwapManager) {
                        // Refresh swap data
                        window.SwapManager.updateTokenBalances();
                    }
                    break;
                case 'liquidity':
                    if (window.LiquidityManager) {
                        // Refresh liquidity data
                        window.LiquidityManager.updateTokenBalances();
                    }
                    break;
                // Add other pages as needed
            }

            // Update wallet balance
            if (window.uiManager) {
                window.uiManager.updateUserBalance();
            }
        } catch (error) {
            console.error('Error refreshing page data:', error);
        }
    }

    // Update footer with current year
    updateFooter() {
        const currentYearEl = document.getElementById('currentYear');
        if (currentYearEl) {
            currentYearEl.textContent = new Date().getFullYear();
        }
    }

    // Show initialization error
    showInitializationError(error) {
        const errorMessage = `
            <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-red-900 border border-red-500 rounded-lg p-6 max-w-md mx-4">
                    <h3 class="text-white font-bold text-lg mb-2">Initialization Error</h3>
                    <p class="text-red-200 mb-4">Failed to initialize The King's Dex. Please refresh the page and try again.</p>
                    <p class="text-red-300 text-sm mb-4">Error: ${error.message}</p>
                    <button onclick="window.location.reload()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded">
                        Refresh Page
                    </button>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', errorMessage);
    }

    // Get current application state
    getState() {
        return {
            isInitialized: this.isInitialized,
            currentPage: this.currentPage,
            walletConnected: window.walletProvider ? window.walletProvider.isWalletConnected() : false,
            currentAccount: window.walletProvider ? window.walletProvider.getCurrentAccount() : null,
            chainId: window.walletProvider ? window.walletProvider.getCurrentChainId() : null
        };
    }

    // Debug information
    getDebugInfo() {
        return {
            ...this.getState(),
            config: window.DexConfig,
            availableTokens: window.TokenUtils ? window.TokenUtils.getCurrentNetworkTokens().length : 0,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
        };
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // Create global app instance
        window.kingsDexApp = new KingsDexApp();
        
        // Initialize the application
        await window.kingsDexApp.initialize();
        
        // Make debug info available in console
        console.log('Kings Dex Debug Info:', window.kingsDexApp.getDebugInfo());
        
    } catch (error) {
        console.error('Failed to initialize Kings Dex:', error);
    }
});

// Export for use in other modules
window.KingsDexApp = KingsDexApp;
