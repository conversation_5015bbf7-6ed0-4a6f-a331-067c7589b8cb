<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The King's Dex - User-Friendly DeFi on Ronin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-gray-900 text-cyan-400 font-orbitron">

    <header class="bg-gray-800 shadow-xl fixed top-0 left-0 right-0 z-50">
        <nav class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-20">
                <div class="flex items-center">
                    <img src="assets/images/logo.png" alt="King's Dex Logo" class="h-12 w-auto mr-3 neon-glow-blue-soft">
                    <span class="text-2xl font-bold text-white tracking-wider">The King's Dex</span>
                </div>
                <div class="hidden md:flex items-center space-x-4">
                    <a href="swap.html" id="nav-swap" class="nav-link px-3 py-2 rounded-md text-sm font-medium hover:bg-cyan-700 hover:text-white transition-all duration-300">Swap</a>
                    <a href="liquidity.html" id="nav-liquidity" class="nav-link px-3 py-2 rounded-md text-sm font-medium hover:bg-cyan-700 hover:text-white transition-all duration-300">Liquidity</a>
                    <a href="farm.html" id="nav-farm" class="nav-link px-3 py-2 rounded-md text-sm font-medium hover:bg-cyan-700 hover:text-white transition-all duration-300 opacity-50 cursor-not-allowed" title="Coming Soon">Farm</a>
                     <a href="learn.html" id="nav-learn" class="nav-link px-3 py-2 rounded-md text-sm font-medium hover:bg-cyan-700 hover:text-white transition-all duration-300 opacity-50 cursor-not-allowed" title="Coming Soon">Learn</a>
                </div>
                <div class="flex items-center">
                    <button id="connectWalletBtn" class="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2 px-4 border border-purple-700 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 neon-glow-purple text-xs sm:text-sm">
                        Connect Wallet
                    </button>
                    <div id="userInfo" class="hidden ml-4 text-sm">
                        <span id="userAddress" class="block text-cyan-300"></span>
                        <span id="userBalance" class="block text-xs text-gray-400"></span>
                    </div>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="mobileMenuBtn" class="text-gray-400 hover:text-white focus:outline-none focus:text-white">
                        <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div id="mobileMenu" class="md:hidden hidden pb-3">
                <a href="#swap" class="nav-link-mobile block px-3 py-2 rounded-md text-base font-medium hover:bg-cyan-700 hover:text-white">Swap</a>
                <a href="#liquidity" class="nav-link-mobile block px-3 py-2 rounded-md text-base font-medium hover:bg-cyan-700 hover:text-white">Liquidity</a>
                <a href="#farm" class="nav-link-mobile block px-3 py-2 rounded-md text-base font-medium hover:bg-cyan-700 hover:text-white opacity-50 cursor-not-allowed">Farm</a>
                <a href="#learn" class="nav-link-mobile block px-3 py-2 rounded-md text-base font-medium hover:bg-cyan-700 hover:text-white opacity-50 cursor-not-allowed">Learn</a>
            </div>
        </nav>
    </header>

    <main class="container mx-auto px-4 py-8 space-y-12 page-content">
        <div class="text-center py-12 card-cyber p-8 neon-glow-blue-soft">
            <h2 class="text-4xl md:text-5xl font-orbitron font-bold mb-4 text-accent-cyan">Welcome to The King's Dex</h2>
            <p class="text-lg md:text-xl text-text-secondary mb-8 max-w-2xl mx-auto leading-relaxed">Your gateway to user-friendly Decentralized Finance on the Ronin Network. Swap, provide liquidity, and earn rewards with confidence.</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <a href="swap.html" class="btn btn-cyber-primary px-6 py-3 text-md md:px-8 md:text-lg w-full sm:w-auto">
                    <i class="fas fa-exchange-alt mr-2"></i>Start Swapping
                </a>
                <a href="liquidity.html" class="btn btn-cyber-secondary px-6 py-3 text-md md:px-8 md:text-lg w-full sm:w-auto">
                    <i class="fas fa-water mr-2"></i>Explore Liquidity
                </a>
            </div>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="card-cyber p-6 hover:neon-glow-blue-soft transition-all duration-300">
                <h3 class="section-title text-accent-cyan text-xl md:text-2xl mb-3 border-accent-cyan">
                    <i class="fas fa-shield-alt mr-2"></i>Secure & Efficient
                </h3>
                <p class="text-text-secondary leading-relaxed">Built on the Ronin Network for fast, low-cost transactions. Your assets are secured by our deployed Uniswap V2 contracts with Katana DEX fallback.</p>
            </div>
            <div class="card-cyber p-6 hover:neon-glow-purple-soft transition-all duration-300">
                <h3 class="section-title text-accent-magenta text-xl md:text-2xl mb-3">
                    <i class="fas fa-check-circle mr-2"></i>Curated & Simple
                </h3>
                <p class="text-text-secondary leading-relaxed">We select quality tokens and simplify DeFi concepts, helping you make informed decisions in our Kingdom. <a href="learn.html" class="text-accent-magenta hover:underline font-semibold">Learn more</a>.</p>
            </div>
            <div class="card-cyber p-6 hover:neon-glow-blue-soft transition-all duration-300">
                <h3 class="section-title text-accent-blue text-xl md:text-2xl mb-3 border-accent-blue">
                    <i class="fas fa-coins mr-2"></i>Earn with $NXS
                </h3>
                <p class="text-text-secondary leading-relaxed">Our $Nexus (NXS) token powers the ecosystem. Provide liquidity and stake to earn rewards and grow with us. (Farming coming in Phase 2 with our contracts).</p>
            </div>
        </div>

        <div class="text-center py-10 card-cyber neon-glow-green-soft">
            <h3 class="text-2xl md:text-3xl font-orbitron text-accent-green mb-4">New to DeFi? We're here to help!</h3>
            <p class="text-text-secondary mb-6 max-w-xl mx-auto leading-relaxed">Understand the basics of decentralized exchanges, liquidity pools, and earning strategies with our easy-to-follow guides.</p>
            <a href="learn.html" class="btn btn-cyber-primary px-8 py-3 text-lg" style="background: linear-gradient(135deg, var(--accent-green), var(--accent-cyan));">
                <i class="fas fa-graduation-cap mr-2"></i>Visit Learning Hub
            </a>
        </div>

        <div>
            <h3 class="text-2xl section-title text-center my-8 !border-accent-cyan">Kingdom's Treasury Stats</h3>
            <div id="dexStatsContainer" class="grid md:grid-cols-3 gap-6 text-center">
                <div class="bg-bg-element-dark p-6 rounded-lg border border-border-cyber hover:border-accent-cyan transition-all duration-300">
                    <p class="text-3xl font-orbitron text-accent-cyan" id="totalValueLockedStat">Loading...</p>
                    <p class="text-text-secondary mt-1">Total Value Locked</p>
                </div>
                <div class="bg-bg-element-dark p-6 rounded-lg border border-border-cyber hover:border-accent-magenta transition-all duration-300">
                    <p class="text-3xl font-orbitron text-accent-magenta" id="totalVolumeStat">Loading...</p>
                    <p class="text-text-secondary mt-1">24h Volume</p>
                </div>
                <div class="bg-bg-element-dark p-6 rounded-lg border border-border-cyber hover:border-accent-blue transition-all duration-300">
                    <p class="text-3xl font-orbitron text-accent-blue" id="nxsPriceStat">Loading...</p>
                    <p class="text-text-secondary mt-1">$NXS Price</p>
                </div>
            </div>
        </div>
    </main>

    <div id="messageModal" class="modal-backdrop">
        <div class="modal-content">
             <h3 id="modalTitle" class="section-title text-xl mb-4">Notification</h3>
            <p id="modalMessageText" class="mb-4">Default message.</p>
            <p class="text-xs text-text-secondary mt-2 hidden" id="modalGasFeeContainer"> Estimated Gas Fee: <span id="modalEstimatedGasFee" class="text-text-primary">-- RON</span>
            </p>
            <div id="modalActionSpinner" class="hidden my-3 animate-spin rounded-full h-8 w-8 border-b-2 border-accent-cyan mx-auto"></div>
            <div id="modalTransactionLinkContainer" class="hidden my-3 text-sm">
                <a href="#" id="modalTransactionLink" target="_blank" rel="noopener noreferrer" class="text-accent-blue hover:underline">View on Ronin Explorer</a>
            </div>
            <button id="closeModalBtn" class="btn btn-cyber-secondary px-6 py-2.5 text-sm mt-3">Close</button>
        </div>
    </div>



    <!-- Web3 Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/web3@1.10.0/dist/web3.min.js"></script>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-functions-compat.js"></script>

    <!-- Token Selection Modal -->
    <div id="tokenSelectModal" class="modal hidden">
        <div class="bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4 border border-cyan-500">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-white">Select Token</h3>
                <button id="closeTokenSelectModalBtn" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mb-4">
                <input
                    type="text"
                    id="tokenSearchInput"
                    placeholder="Search tokens..."
                    class="w-full bg-gray-700 text-white px-4 py-2 rounded-lg border border-gray-600 focus:border-cyan-500 outline-none"
                >
            </div>
            <div id="tokenList" class="max-h-64 overflow-y-auto custom-scrollbar space-y-2">
                <!-- Token list will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Info Modal -->
    <div id="infoModal" class="modal hidden">
        <div class="bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4 border border-cyan-500">
            <div class="flex justify-between items-center mb-4">
                <h3 id="infoModalTitle" class="text-xl font-bold text-white">Information</h3>
                <button id="closeInfoModalBtn" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <p id="infoModalMessage" class="text-gray-300 mb-4">Message content</p>
            <div id="infoModalTxLinkContainer" class="mb-4 hidden">
                <a id="infoModalTxLink" href="#" target="_blank" class="text-cyan-400 hover:text-cyan-300 text-sm">
                    <i class="fas fa-external-link-alt mr-1"></i>
                    View on Explorer
                </a>
            </div>
            <button id="infoModalOkBtn" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg">
                OK
            </button>
        </div>
    </div>

    <!-- Loading Modal -->
    <div id="loadingModal" class="modal hidden">
        <div class="bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4 border border-cyan-500 text-center">
            <div class="mb-4">
                <div class="loader border-4 border-gray-600 border-t-cyan-400 rounded-full w-12 h-12 mx-auto"></div>
            </div>
            <h3 id="loadingModalTitle" class="text-xl font-bold text-white mb-2">Loading</h3>
            <p id="loadingModalMessage" class="text-gray-300">Please wait...</p>
        </div>
    </div>

    <!-- Slippage Settings Modal -->
    <div id="slippageModal" class="modal hidden">
        <div class="bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4 border border-cyan-500">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-white">Slippage Settings</h3>
                <button id="closeSlippageModalBtn" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mb-4">
                <label class="block text-sm text-gray-300 mb-2">Slippage Tolerance (%)</label>
                <div class="flex space-x-2 mb-3">
                    <button class="slippage-btn px-3 py-1 rounded text-sm" data-value="0.1">0.1%</button>
                    <button class="slippage-btn px-3 py-1 rounded text-sm" data-value="0.5">0.5%</button>
                    <button class="slippage-btn px-3 py-1 rounded text-sm" data-value="1.0">1.0%</button>
                    <button class="slippage-btn px-3 py-1 rounded text-sm" data-value="3.0">3.0%</button>
                </div>
                <input
                    type="number"
                    id="slippageToleranceInput"
                    placeholder="Custom"
                    step="0.1"
                    min="0.1"
                    max="50"
                    class="w-full bg-gray-700 text-white px-4 py-2 rounded-lg border border-gray-600 focus:border-cyan-500 outline-none"
                >
            </div>
            <button id="saveSlippageBtn" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg">
                Save Settings
            </button>
        </div>
    </div>

    <!-- Enhanced Application Scripts -->
    <script src="js/config.js"></script>
    <script src="js/tokens.js"></script>
    <script src="js/wallet-provider.js"></script>
    <script src="js/utils.js"></script>

    <!-- Frontend Integration -->
    <script type="module" src="main.js"></script>
</body>
</html>
