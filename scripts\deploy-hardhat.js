// scripts/deploy-hardhat.js
// Hardhat deployment script for Uniswap V2 contracts on Ronin

const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🚀 Starting Uniswap V2 deployment on Ronin...");
    
    // Get the deployer account
    const [deployer] = await ethers.getSigners();
    const network = await ethers.provider.getNetwork();
    
    console.log("📋 Deployment Details:");
    console.log("  Network:", network.name, `(Chain ID: ${network.chainId})`);
    console.log("  Deployer:", deployer.address);
    
    // Check deployer balance
    const balance = await deployer.getBalance();
    console.log("  Balance:", ethers.utils.formatEther(balance), "RON");
    
    if (balance.lt(ethers.utils.parseEther("0.1"))) {
        throw new Error("❌ Insufficient balance for deployment. Need at least 0.1 RON");
    }

    // WRON address for different networks
    const WRON_ADDRESSES = {
        2020: "******************************************", // Ronin Mainnet
        2021: "******************************************"  // Ronin Saigon (Testnet)
    };
    
    const WRON_ADDRESS = WRON_ADDRESSES[network.chainId];
    if (!WRON_ADDRESS) {
        throw new Error(`❌ WRON address not configured for chain ID ${network.chainId}`);
    }
    
    console.log("  WRON Address:", WRON_ADDRESS);

    // Get current gas price and add buffer
    const currentGasPrice = await ethers.provider.getGasPrice();
    const gasPrice = currentGasPrice.mul(120).div(100); // Add 20% buffer
    console.log("  Gas Price:", ethers.utils.formatUnits(gasPrice, "gwei"), "gwei");
    console.log("");

    // Deploy UniswapV2Factory
    console.log("📦 Deploying UniswapV2Factory...");
    const UniswapV2Factory = await ethers.getContractFactory("UniswapV2Factory");
    const factory = await UniswapV2Factory.deploy(deployer.address, {
        gasPrice: gasPrice,
        gasLimit: 8000000
    });
    await factory.deployed();
    
    console.log("✅ UniswapV2Factory deployed:");
    console.log("  Address:", factory.address);
    console.log("  Transaction:", factory.deployTransaction.hash);
    console.log("");

    // Deploy UniswapV2Router02
    console.log("📦 Deploying UniswapV2Router02...");
    const UniswapV2Router02 = await ethers.getContractFactory("UniswapV2Router02");
    const router = await UniswapV2Router02.deploy(factory.address, WRON_ADDRESS, {
        gasPrice: gasPrice,
        gasLimit: 8000000
    });
    await router.deployed();
    
    console.log("✅ UniswapV2Router02 deployed:");
    console.log("  Address:", router.address);
    console.log("  Transaction:", router.deployTransaction.hash);
    console.log("");

    // Verify deployments
    console.log("🔍 Verifying deployments...");
    
    // Check factory
    const factoryFeeTo = await factory.feeTo();
    const factoryFeeToSetter = await factory.feeToSetter();
    console.log("  Factory feeTo:", factoryFeeTo);
    console.log("  Factory feeToSetter:", factoryFeeToSetter);
    
    // Check router
    const routerFactory = await router.factory();
    const routerWETH = await router.WETH();
    console.log("  Router factory:", routerFactory);
    console.log("  Router WETH:", routerWETH);
    
    // Verify connections
    if (routerFactory.toLowerCase() !== factory.address.toLowerCase()) {
        throw new Error("❌ Router factory address mismatch!");
    }
    if (routerWETH.toLowerCase() !== WRON_ADDRESS.toLowerCase()) {
        throw new Error("❌ Router WETH address mismatch!");
    }
    
    console.log("✅ All verifications passed!");
    console.log("");

    // Save deployment information
    const deploymentInfo = {
        network: {
            name: network.name,
            chainId: network.chainId
        },
        timestamp: new Date().toISOString(),
        deployer: deployer.address,
        contracts: {
            factory: factory.address,
            router: router.address,
            wron: WRON_ADDRESS
        },
        transactions: {
            factory: factory.deployTransaction.hash,
            router: router.deployTransaction.hash
        },
        gasUsed: {
            factory: factory.deployTransaction.gasLimit?.toString(),
            router: router.deployTransaction.gasLimit?.toString()
        }
    };

    // Create deployments directory if it doesn't exist
    const deploymentsDir = path.join(__dirname, '..', 'deployments');
    if (!fs.existsSync(deploymentsDir)) {
        fs.mkdirSync(deploymentsDir, { recursive: true });
    }

    // Save deployment info
    const networkName = network.chainId === 2020 ? 'mainnet' : 'saigon';
    const deploymentPath = path.join(deploymentsDir, `uniswap-v2-${networkName}.json`);
    fs.writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
    
    console.log("💾 Deployment info saved to:", deploymentPath);
    console.log("");

    // Display summary
    console.log("🎉 Deployment Summary:");
    console.log("=" .repeat(50));
    console.log(`Network: ${network.name} (${network.chainId})`);
    console.log(`Factory: ${factory.address}`);
    console.log(`Router: ${router.address}`);
    console.log(`WRON: ${WRON_ADDRESS}`);
    console.log("=" .repeat(50));
    console.log("");
    
    console.log("📝 Next Steps:");
    console.log("1. Update contract addresses in your configuration files");
    console.log("2. Run the address update script:");
    console.log(`   node scripts/update-contract-addresses.js ${networkName} ${factory.address} ${router.address}`);
    console.log("3. Deploy your cloud functions with the new addresses");
    console.log("4. Test the integration thoroughly");
    console.log("");
    
    console.log("✅ Deployment completed successfully!");
}

// Error handling
main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Deployment failed:");
        console.error(error);
        process.exit(1);
    });
