// UI Management for The King's Dex

class UIManager {
    constructor() {
        this.modals = new Map();
        this.notifications = [];
        this.currentPage = 'swap';
        
        this.initializeUI();
    }

    // Initialize UI components
    initializeUI() {
        this.initializeModals();
        this.initializeNavigation();
        this.initializeWalletUI();
        this.initializeNotifications();
    }

    // Initialize modal system
    initializeModals() {
        // Token selection modal
        this.modals.set('tokenSelect', {
            element: document.getElementById('tokenSelectModal'),
            isOpen: false,
            context: null
        });

        // Info/notification modal
        this.modals.set('info', {
            element: document.getElementById('infoModal'),
            isOpen: false
        });

        // Loading modal
        this.modals.set('loading', {
            element: document.getElementById('loadingModal'),
            isOpen: false
        });

        // Slippage settings modal
        this.modals.set('slippage', {
            element: document.getElementById('slippageModal'),
            isOpen: false
        });

        // Add click outside to close functionality
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeAllModals();
            }
        });

        // Add escape key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }

    // Initialize navigation
    initializeNavigation() {
        const navLinks = document.querySelectorAll('.nav-link, .nav-link-mobile');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const href = link.getAttribute('href');
                if (href && href.startsWith('#')) {
                    this.navigateToPage(href.substring(1));
                }
            });
        });

        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');
        
        if (mobileMenuBtn && mobileMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
        }
    }

    // Initialize wallet UI
    initializeWalletUI() {
        const connectBtn = document.getElementById('connectWalletBtn');
        if (connectBtn) {
            connectBtn.addEventListener('click', () => {
                this.connectWallet();
            });
        }

        // Listen for wallet events
        if (window.walletProvider) {
            window.walletProvider.on('connect', (data) => {
                this.updateWalletUI(data.account);
                this.showNotification('Wallet Connected', `Connected: ${window.Utils.truncateAddress(data.account)}`, 'success');
            });

            window.walletProvider.on('disconnect', () => {
                this.updateWalletUI(null);
                this.showNotification('Wallet Disconnected', 'Your wallet has been disconnected', 'info');
            });

            window.walletProvider.on('accountsChanged', (accounts) => {
                if (accounts.length > 0) {
                    this.updateWalletUI(accounts[0]);
                } else {
                    this.updateWalletUI(null);
                }
            });

            window.walletProvider.on('chainChanged', (chainId) => {
                const chainIdNumber = parseInt(chainId, 16);
                if (chainIdNumber !== window.DexConfig.TARGET_CHAIN_ID) {
                    this.showNotification(
                        'Wrong Network',
                        `Please switch to ${window.DexConfig.NETWORK_NAMES[window.DexConfig.TARGET_CHAIN_ID]}`,
                        'warning'
                    );
                }
            });

            window.walletProvider.on('error', (error) => {
                this.showNotification('Wallet Error', window.Utils.handleError(error), 'error');
            });
        }
    }

    // Initialize notification system
    initializeNotifications() {
        // Create notification container if it doesn't exist
        if (!document.getElementById('notificationContainer')) {
            const container = document.createElement('div');
            container.id = 'notificationContainer';
            container.className = 'fixed top-4 right-4 z-50 space-y-2';
            document.body.appendChild(container);
        }
    }

    // Navigation methods
    navigateToPage(page) {
        // Hide all page sections
        const pageElements = document.querySelectorAll('.page-content');
        pageElements.forEach(el => el.classList.add('hidden'));

        // Show target page
        const targetPage = document.getElementById(`page-${page}`);
        if (targetPage) {
            targetPage.classList.remove('hidden');
            this.currentPage = page;
        }

        // Update navigation active state
        this.updateNavigationState(page);

        // Initialize page-specific functionality
        this.initializePage(page);
    }

    updateNavigationState(activePage) {
        const navLinks = document.querySelectorAll('.nav-link, .nav-link-mobile');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href === `#${activePage}`) {
                link.classList.add('active-nav-link');
            } else {
                link.classList.remove('active-nav-link');
            }
        });
    }

    initializePage(page) {
        switch (page) {
            case 'swap':
                if (window.SwapManager) {
                    window.SwapManager.initialize();
                }
                break;
            case 'liquidity':
                if (window.LiquidityManager) {
                    window.LiquidityManager.initialize();
                }
                break;
            case 'farm':
                // Initialize farming page when implemented
                break;
            case 'learn':
                // Initialize learn page when implemented
                break;
        }
    }

    // Wallet UI methods
    async connectWallet() {
        try {
            this.showLoadingModal('Connecting Wallet', 'Please approve the connection in your wallet...');
            
            const result = await window.walletProvider.connect();
            
            this.closeModal('loading');
            
            if (result) {
                this.updateWalletUI(result.account);
            }
        } catch (error) {
            this.closeModal('loading');
            this.showNotification('Connection Failed', window.Utils.handleError(error), 'error');
        }
    }

    updateWalletUI(account) {
        const connectBtn = document.getElementById('connectWalletBtn');
        const userInfo = document.getElementById('userInfo');
        const userAddress = document.getElementById('userAddress');
        const userBalance = document.getElementById('userBalance');

        if (account) {
            // Show connected state
            if (connectBtn) connectBtn.classList.add('hidden');
            if (userInfo) userInfo.classList.remove('hidden');
            if (userAddress) userAddress.textContent = window.Utils.truncateAddress(account);

            // Update balance
            this.updateUserBalance();
        } else {
            // Show disconnected state
            if (connectBtn) connectBtn.classList.remove('hidden');
            if (userInfo) userInfo.classList.add('hidden');
            if (userAddress) userAddress.textContent = '';
            if (userBalance) userBalance.textContent = '';
        }
    }

    async updateUserBalance() {
        try {
            const balance = await window.walletProvider.getBalance();
            const userBalance = document.getElementById('userBalance');
            if (userBalance) {
                userBalance.textContent = `${window.Utils.formatNumber(balance, 4)} RON`;
            }
        } catch (error) {
            console.error('Error updating balance:', error);
        }
    }

    // Modal methods
    openModal(modalName, options = {}) {
        const modal = this.modals.get(modalName);
        if (!modal || !modal.element) return;

        modal.element.classList.remove('hidden');
        modal.isOpen = true;

        // Handle specific modal types
        switch (modalName) {
            case 'tokenSelect':
                this.setupTokenSelectModal(options);
                break;
            case 'slippage':
                this.setupSlippageModal(options);
                break;
        }
    }

    closeModal(modalName) {
        const modal = this.modals.get(modalName);
        if (!modal || !modal.element) return;

        modal.element.classList.add('hidden');
        modal.isOpen = false;
        modal.context = null;
    }

    closeAllModals() {
        this.modals.forEach((modal, name) => {
            this.closeModal(name);
        });
    }

    showInfoModal(title, message, isError = false, txHash = null) {
        const modal = this.modals.get('info');
        if (!modal || !modal.element) return;

        const titleEl = document.getElementById('infoModalTitle');
        const messageEl = document.getElementById('infoModalMessage');
        const txLinkContainer = document.getElementById('infoModalTxLinkContainer');
        const txLink = document.getElementById('infoModalTxLink');

        if (titleEl) titleEl.textContent = title;
        if (messageEl) messageEl.textContent = message;

        // Handle transaction link
        if (txHash && txLinkContainer && txLink) {
            txLink.href = window.Utils.getExplorerUrl(txHash);
            txLinkContainer.classList.remove('hidden');
        } else if (txLinkContainer) {
            txLinkContainer.classList.add('hidden');
        }

        // Style based on error state
        if (titleEl) {
            titleEl.className = isError 
                ? 'text-xl font-semibold text-red-400 mb-4'
                : 'text-xl font-semibold text-green-400 mb-4';
        }

        this.openModal('info');
    }

    showLoadingModal(title, message = 'Please wait...') {
        const modal = this.modals.get('loading');
        if (!modal || !modal.element) return;

        const titleEl = document.getElementById('loadingModalTitle');
        const messageEl = document.getElementById('loadingModalMessage');

        if (titleEl) titleEl.textContent = title;
        if (messageEl) messageEl.textContent = message;

        this.openModal('loading');
    }

    // Notification methods
    showNotification(title, message, type = 'info', duration = 5000) {
        const container = document.getElementById('notificationContainer');
        if (!container) return;

        const notification = this.createNotificationElement(title, message, type);
        container.appendChild(notification);

        // Auto-remove after duration
        setTimeout(() => {
            this.removeNotification(notification);
        }, duration);

        // Add to notifications array
        this.notifications.push({
            element: notification,
            timestamp: Date.now()
        });
    }

    createNotificationElement(title, message, type) {
        const notification = document.createElement('div');
        notification.className = `notification bg-gray-800 border-l-4 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;
        
        // Set border color based on type
        const borderColors = {
            success: 'border-green-500',
            error: 'border-red-500',
            warning: 'border-yellow-500',
            info: 'border-blue-500'
        };
        
        notification.classList.add(borderColors[type] || borderColors.info);

        notification.innerHTML = `
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <h4 class="text-white font-semibold text-sm">${title}</h4>
                    <p class="text-gray-300 text-xs mt-1">${message}</p>
                </div>
                <button class="text-gray-400 hover:text-white ml-2" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        return notification;
    }

    removeNotification(notification) {
        if (notification && notification.parentElement) {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.parentElement.removeChild(notification);
                }
            }, 300);
        }
    }

    // Token selection modal setup
    setupTokenSelectModal(options) {
        const { context, excludeToken, onSelect } = options;
        
        const modal = this.modals.get('tokenSelect');
        modal.context = { context, excludeToken, onSelect };

        // Populate token list
        this.populateTokenList();

        // Setup search functionality
        const searchInput = document.getElementById('tokenSearchInput');
        if (searchInput) {
            searchInput.value = '';
            searchInput.addEventListener('input', window.Utils.debounce(() => {
                this.filterTokenList(searchInput.value);
            }, 300));
        }
    }

    populateTokenList() {
        const tokenList = document.getElementById('tokenList');
        if (!tokenList) return;

        const tokens = window.TokenUtils.getSwapTokens();
        const modal = this.modals.get('tokenSelect');
        const excludeToken = modal.context?.excludeToken;

        tokenList.innerHTML = '';

        tokens.forEach(token => {
            if (excludeToken && token.symbol === excludeToken.symbol) return;

            const tokenItem = this.createTokenListItem(token);
            tokenList.appendChild(tokenItem);
        });
    }

    createTokenListItem(token) {
        const item = document.createElement('div');
        item.className = 'flex items-center p-3 hover:bg-gray-700 cursor-pointer rounded-lg transition-colors';
        
        item.innerHTML = `
            <img src="${token.icon}" alt="${token.symbol}" class="w-8 h-8 rounded-full mr-3" onerror="this.src='https://placehold.co/32x32/718096/FFFFFF?text=${token.symbol.charAt(0)}'">
            <div class="flex-1">
                <div class="text-white font-medium">${token.symbol}</div>
                <div class="text-gray-400 text-sm">${token.name}</div>
            </div>
            <div class="text-right">
                <div class="text-white" id="balance-${token.address}">0.00</div>
                <div class="text-gray-400 text-xs">${window.Utils.truncateAddress(token.address)}</div>
            </div>
        `;

        item.addEventListener('click', () => {
            const modal = this.modals.get('tokenSelect');
            if (modal.context?.onSelect) {
                modal.context.onSelect(token);
            }
            this.closeModal('tokenSelect');
        });

        // Load token balance if wallet is connected
        if (window.walletProvider.isWalletConnected()) {
            this.loadTokenBalance(token, item);
        }

        return item;
    }

    async loadTokenBalance(token, itemElement) {
        try {
            const balance = await window.walletProvider.getTokenBalance(token.address);
            const balanceElement = itemElement.querySelector(`#balance-${token.address}`);
            if (balanceElement) {
                balanceElement.textContent = window.Utils.formatNumber(balance, 4);
            }
        } catch (error) {
            console.error('Error loading token balance:', error);
        }
    }

    filterTokenList(query) {
        const tokens = window.TokenUtils.searchTokens(query);
        const tokenList = document.getElementById('tokenList');
        if (!tokenList) return;

        const modal = this.modals.get('tokenSelect');
        const excludeToken = modal.context?.excludeToken;

        tokenList.innerHTML = '';

        tokens.forEach(token => {
            if (excludeToken && token.symbol === excludeToken.symbol) return;

            const tokenItem = this.createTokenListItem(token);
            tokenList.appendChild(tokenItem);
        });
    }

    // Slippage modal setup
    setupSlippageModal(options) {
        const currentSlippage = window.Utils.loadFromStorage('kingsDexSlippage', window.DexConfig.DEFAULT_SLIPPAGE);

        const slippageInput = document.getElementById('slippageToleranceInput');
        if (slippageInput) {
            slippageInput.value = currentSlippage;
        }

        // Setup slippage buttons
        const slippageButtons = document.querySelectorAll('.slippage-btn');
        slippageButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const value = btn.getAttribute('data-value');
                if (slippageInput) {
                    slippageInput.value = value;
                }

                // Update button states
                slippageButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        // Setup save button
        const saveBtn = document.getElementById('saveSlippageBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                const slippage = slippageInput?.value;
                if (slippage) {
                    const error = window.Utils.validateSlippage(slippage);
                    if (error) {
                        this.showNotification('Invalid Slippage', error, 'error');
                        return;
                    }

                    window.Utils.saveToStorage('kingsDexSlippage', parseFloat(slippage));
                    this.showNotification('Settings Saved', `Slippage tolerance set to ${slippage}%`, 'success');
                    this.closeModal('slippage');
                }
            });
        }
    }

    // Auto-connect wallet on page load
    async autoConnectWallet() {
        try {
            const connected = await window.walletProvider.autoConnect();
            if (connected) {
                this.updateWalletUI(window.walletProvider.getCurrentAccount());
            }
        } catch (error) {
            console.error('Auto-connect failed:', error);
        }
    }
}
}

// Initialize UI Manager
window.uiManager = new UIManager();

// Export for use in other modules
window.UIManager = UIManager;
