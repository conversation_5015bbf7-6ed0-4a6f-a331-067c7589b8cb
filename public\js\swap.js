// Swap functionality for The King's Dex

class SwapManager {
    constructor() {
        this.fromToken = null;
        this.toToken = null;
        this.fromAmount = '';
        this.toAmount = '';
        this.slippage = window.Utils.loadFromStorage('kingsDexSlippage', window.DexConfig.DEFAULT_SLIPPAGE);
        this.priceInfo = null;
        this.quoteTimer = null;
        this.isInitialized = false;
    }

    // Initialize swap interface
    initialize() {
        if (this.isInitialized) return;

        this.createSwapInterface();
        this.setupEventListeners();
        this.loadDefaultTokens();
        this.isInitialized = true;
    }

    // Create swap interface HTML
    createSwapInterface() {
        const container = document.getElementById('swap-component-container');
        if (!container) return;

        container.innerHTML = `
            <div class="max-w-md mx-auto bg-gray-800 rounded-xl shadow-xl border border-cyan-500 p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-white">Swap Tokens</h2>
                    <button id="swapSettingsBtn" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-cog text-lg"></i>
                    </button>
                </div>

                <form id="swapForm" class="space-y-4">
                    <!-- From Token Section -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <div class="flex justify-between items-center mb-2">
                            <label class="text-sm text-gray-300">From</label>
                            <span class="text-xs text-gray-400" id="fromTokenBalance">Balance: 0.00</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <input 
                                type="number" 
                                id="fromAmount" 
                                placeholder="0.0" 
                                class="flex-1 bg-transparent text-white text-xl font-semibold outline-none"
                                step="any"
                                min="0"
                            >
                            <button 
                                type="button" 
                                id="fromTokenSelect" 
                                class="flex items-center space-x-2 bg-gray-600 hover:bg-gray-500 px-3 py-2 rounded-lg transition-colors"
                            >
                                <img id="fromTokenIcon" src="" alt="" class="w-6 h-6 rounded-full hidden">
                                <span id="fromTokenSymbol" class="text-white font-medium">Select Token</span>
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </button>
                        </div>
                        <div class="flex justify-between items-center mt-2">
                            <span class="text-xs text-gray-500" id="fromTokenUSD">$0.00</span>
                            <button type="button" id="useMaxBtn" class="text-xs text-cyan-400 hover:text-cyan-300">MAX</button>
                        </div>
                    </div>

                    <!-- Swap Direction Button -->
                    <div class="flex justify-center">
                        <button 
                            type="button" 
                            id="swapDirectionBtn" 
                            class="bg-gray-700 hover:bg-gray-600 p-2 rounded-full transition-colors"
                        >
                            <i class="fas fa-arrow-down text-cyan-400"></i>
                        </button>
                    </div>

                    <!-- To Token Section -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <div class="flex justify-between items-center mb-2">
                            <label class="text-sm text-gray-300">To</label>
                            <span class="text-xs text-gray-400" id="toTokenBalance">Balance: 0.00</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <input 
                                type="number" 
                                id="toAmount" 
                                placeholder="0.0" 
                                class="flex-1 bg-transparent text-white text-xl font-semibold outline-none"
                                readonly
                            >
                            <button 
                                type="button" 
                                id="toTokenSelect" 
                                class="flex items-center space-x-2 bg-gray-600 hover:bg-gray-500 px-3 py-2 rounded-lg transition-colors"
                            >
                                <img id="toTokenIcon" src="" alt="" class="w-6 h-6 rounded-full hidden">
                                <span id="toTokenSymbol" class="text-white font-medium">Select Token</span>
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </button>
                        </div>
                        <div class="flex justify-between items-center mt-2">
                            <span class="text-xs text-gray-500" id="toTokenUSD">$0.00</span>
                        </div>
                    </div>

                    <!-- Price Info -->
                    <div id="priceInfoContainer" class="bg-gray-700 rounded-lg p-4 hidden">
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-300">Rate</span>
                                <span class="text-white" id="exchangeRate">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-300">Price Impact</span>
                                <span class="text-white" id="priceImpact">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-300">Minimum Received</span>
                                <span class="text-white" id="minimumReceived">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-300">LP Fee</span>
                                <span class="text-white" id="lpFee">0.3%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Protocol Info -->
                    <div id="protocolInfo" class="hidden">
                        <div class="flex items-center justify-center space-x-2 text-sm text-cyan-400">
                            <i class="fas fa-exchange-alt"></i>
                            <span id="protocolName">Uniswap V2</span>
                        </div>
                    </div>

                    <!-- Swap Button -->
                    <button 
                        type="submit" 
                        id="swapBtn" 
                        class="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors"
                        disabled
                    >
                        Connect Wallet
                    </button>
                </form>
            </div>
        `;
    }

    // Setup event listeners
    setupEventListeners() {
        // Token selection buttons
        document.getElementById('fromTokenSelect')?.addEventListener('click', () => {
            this.openTokenSelectModal('from');
        });

        document.getElementById('toTokenSelect')?.addEventListener('click', () => {
            this.openTokenSelectModal('to');
        });

        // Swap direction button
        document.getElementById('swapDirectionBtn')?.addEventListener('click', () => {
            this.swapTokenDirection();
        });

        // Amount input
        document.getElementById('fromAmount')?.addEventListener('input', (e) => {
            this.fromAmount = e.target.value;
            this.debouncedGetQuote();
            this.updateSwapButton();
        });

        // Use max button
        document.getElementById('useMaxBtn')?.addEventListener('click', () => {
            this.useMaxAmount();
        });

        // Settings button
        document.getElementById('swapSettingsBtn')?.addEventListener('click', () => {
            window.uiManager.openModal('slippage');
        });

        // Swap form submission
        document.getElementById('swapForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.executeSwap();
        });

        // Listen for wallet connection changes
        if (window.walletProvider) {
            window.walletProvider.on('connect', () => {
                this.updateSwapButton();
                this.updateTokenBalances();
            });

            window.walletProvider.on('disconnect', () => {
                this.updateSwapButton();
                this.clearTokenBalances();
            });
        }
    }

    // Load default tokens
    loadDefaultTokens() {
        const tokens = window.TokenUtils.getSwapTokens();
        if (tokens.length >= 2) {
            // Set RON as default from token
            const ronToken = tokens.find(t => t.isNative);
            if (ronToken) {
                this.selectToken('from', ronToken);
            }

            // Set USDC as default to token if available
            const usdcToken = tokens.find(t => t.symbol === 'USDC');
            if (usdcToken) {
                this.selectToken('to', usdcToken);
            }
        }
    }

    // Open token selection modal
    openTokenSelectModal(type) {
        const excludeToken = type === 'from' ? this.toToken : this.fromToken;
        
        window.uiManager.openModal('tokenSelect', {
            context: type,
            excludeToken: excludeToken,
            onSelect: (token) => {
                this.selectToken(type, token);
            }
        });
    }

    // Select a token
    selectToken(type, token) {
        if (type === 'from') {
            this.fromToken = token;
            this.updateTokenUI('from', token);
        } else {
            this.toToken = token;
            this.updateTokenUI('to', token);
        }

        this.updateTokenBalances();
        this.debouncedGetQuote();
        this.updateSwapButton();
    }

    // Update token UI
    updateTokenUI(type, token) {
        const symbolEl = document.getElementById(`${type}TokenSymbol`);
        const iconEl = document.getElementById(`${type}TokenIcon`);

        if (symbolEl) symbolEl.textContent = token.symbol;
        if (iconEl) {
            iconEl.src = token.icon;
            iconEl.alt = token.symbol;
            iconEl.classList.remove('hidden');
        }
    }

    // Swap token direction
    swapTokenDirection() {
        const tempToken = this.fromToken;
        const tempAmount = this.fromAmount;

        this.fromToken = this.toToken;
        this.toToken = tempToken;
        this.fromAmount = this.toAmount;
        this.toAmount = tempAmount;

        // Update UI
        if (this.fromToken) this.updateTokenUI('from', this.fromToken);
        if (this.toToken) this.updateTokenUI('to', this.toToken);

        const fromAmountEl = document.getElementById('fromAmount');
        const toAmountEl = document.getElementById('toAmount');

        if (fromAmountEl) fromAmountEl.value = this.fromAmount;
        if (toAmountEl) toAmountEl.value = this.toAmount;

        this.updateTokenBalances();
        this.debouncedGetQuote();
        this.updateSwapButton();
    }

    // Use maximum amount
    async useMaxAmount() {
        if (!this.fromToken || !window.walletProvider.isWalletConnected()) return;

        try {
            const balance = await window.walletProvider.getTokenBalance(this.fromToken.address);
            
            // Reserve some RON for gas if swapping native token
            let maxAmount = parseFloat(balance);
            if (this.fromToken.isNative && maxAmount > 0.01) {
                maxAmount -= 0.01; // Reserve 0.01 RON for gas
            }

            this.fromAmount = maxAmount.toString();
            const fromAmountEl = document.getElementById('fromAmount');
            if (fromAmountEl) fromAmountEl.value = this.fromAmount;

            this.debouncedGetQuote();
            this.updateSwapButton();
        } catch (error) {
            console.error('Error getting max amount:', error);
        }
    }

    // Debounced quote fetching
    debouncedGetQuote() {
        if (this.quoteTimer) {
            clearTimeout(this.quoteTimer);
        }

        this.quoteTimer = setTimeout(() => {
            this.getQuote();
        }, 500);
    }

    // Get swap quote
    async getQuote() {
        if (!this.fromToken || !this.toToken || !this.fromAmount || parseFloat(this.fromAmount) <= 0) {
            this.clearQuote();
            return;
        }

        try {
            const toAmountEl = document.getElementById('toAmount');
            if (toAmountEl) toAmountEl.value = 'Fetching...';

            // This would typically call your backend API or directly interact with DEX contracts
            // For now, we'll simulate a quote
            const quote = await this.simulateQuote();

            this.toAmount = quote.outputAmount;
            this.priceInfo = quote;

            if (toAmountEl) toAmountEl.value = window.Utils.formatNumber(quote.outputAmount, 6);

            this.updatePriceInfo(quote);
            this.updateProtocolInfo(quote.protocol);

        } catch (error) {
            console.error('Error getting quote:', error);
            const toAmountEl = document.getElementById('toAmount');
            if (toAmountEl) toAmountEl.value = 'Error';
            this.clearQuote();
        }
    }

    // Simulate quote (replace with actual DEX integration)
    async simulateQuote() {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        const inputAmount = parseFloat(this.fromAmount);
        const rate = 0.95; // Simulate 5% slippage
        const outputAmount = inputAmount * rate;

        return {
            inputAmount: inputAmount,
            outputAmount: outputAmount,
            rate: rate,
            priceImpact: 0.1,
            minimumReceived: outputAmount * (1 - this.slippage / 100),
            protocol: 'Uniswap V2',
            fee: 0.003
        };
    }

    // Clear quote information
    clearQuote() {
        this.toAmount = '';
        this.priceInfo = null;

        const toAmountEl = document.getElementById('toAmount');
        const priceInfoContainer = document.getElementById('priceInfoContainer');
        const protocolInfo = document.getElementById('protocolInfo');

        if (toAmountEl) toAmountEl.value = '';
        if (priceInfoContainer) priceInfoContainer.classList.add('hidden');
        if (protocolInfo) protocolInfo.classList.add('hidden');
    }

    // Update price information display
    updatePriceInfo(quote) {
        const container = document.getElementById('priceInfoContainer');
        if (!container) return;

        const exchangeRateEl = document.getElementById('exchangeRate');
        const priceImpactEl = document.getElementById('priceImpact');
        const minimumReceivedEl = document.getElementById('minimumReceived');
        const lpFeeEl = document.getElementById('lpFee');

        if (exchangeRateEl) {
            exchangeRateEl.textContent = `1 ${this.fromToken.symbol} = ${window.Utils.formatNumber(quote.rate, 6)} ${this.toToken.symbol}`;
        }

        if (priceImpactEl) {
            const impact = quote.priceImpact;
            priceImpactEl.textContent = `${impact.toFixed(2)}%`;
            priceImpactEl.className = impact > 5 ? 'text-red-400' : impact > 2 ? 'text-yellow-400' : 'text-green-400';
        }

        if (minimumReceivedEl) {
            minimumReceivedEl.textContent = `${window.Utils.formatNumber(quote.minimumReceived, 6)} ${this.toToken.symbol}`;
        }

        if (lpFeeEl) {
            lpFeeEl.textContent = `${(quote.fee * 100).toFixed(2)}%`;
        }

        container.classList.remove('hidden');
    }

    // Update protocol information
    updateProtocolInfo(protocolName) {
        const container = document.getElementById('protocolInfo');
        const nameEl = document.getElementById('protocolName');

        if (nameEl) nameEl.textContent = protocolName;
        if (container) container.classList.remove('hidden');
    }

    // Update token balances
    async updateTokenBalances() {
        if (!window.walletProvider.isWalletConnected()) return;

        try {
            if (this.fromToken) {
                const balance = await window.walletProvider.getTokenBalance(this.fromToken.address);
                const balanceEl = document.getElementById('fromTokenBalance');
                if (balanceEl) {
                    balanceEl.textContent = `Balance: ${window.Utils.formatNumber(balance, 4)}`;
                }
            }

            if (this.toToken) {
                const balance = await window.walletProvider.getTokenBalance(this.toToken.address);
                const balanceEl = document.getElementById('toTokenBalance');
                if (balanceEl) {
                    balanceEl.textContent = `Balance: ${window.Utils.formatNumber(balance, 4)}`;
                }
            }
        } catch (error) {
            console.error('Error updating token balances:', error);
        }
    }

    // Clear token balances
    clearTokenBalances() {
        const fromBalanceEl = document.getElementById('fromTokenBalance');
        const toBalanceEl = document.getElementById('toTokenBalance');

        if (fromBalanceEl) fromBalanceEl.textContent = 'Balance: 0.00';
        if (toBalanceEl) toBalanceEl.textContent = 'Balance: 0.00';
    }

    // Update swap button state
    updateSwapButton() {
        const swapBtn = document.getElementById('swapBtn');
        if (!swapBtn) return;

        if (!window.walletProvider.isWalletConnected()) {
            swapBtn.textContent = 'Connect Wallet';
            swapBtn.disabled = false;
            return;
        }

        if (!this.fromToken) {
            swapBtn.textContent = 'Select a token';
            swapBtn.disabled = true;
            return;
        }

        if (!this.toToken) {
            swapBtn.textContent = 'Select a token';
            swapBtn.disabled = true;
            return;
        }

        if (!this.fromAmount || parseFloat(this.fromAmount) <= 0) {
            swapBtn.textContent = 'Enter an amount';
            swapBtn.disabled = true;
            return;
        }

        if (!this.toAmount || !this.priceInfo) {
            swapBtn.textContent = 'Getting quote...';
            swapBtn.disabled = true;
            return;
        }

        swapBtn.textContent = 'Swap';
        swapBtn.disabled = false;
    }

    // Execute swap transaction
    async executeSwap() {
        if (!window.walletProvider.isWalletConnected()) {
            await window.uiManager.connectWallet();
            return;
        }

        if (!this.fromToken || !this.toToken || !this.fromAmount || !this.priceInfo) {
            window.uiManager.showNotification('Swap Error', 'Please complete all fields', 'error');
            return;
        }

        try {
            window.uiManager.showLoadingModal('Preparing Swap', 'Estimating gas costs...');

            // This would integrate with your actual DEX contracts
            // For now, we'll simulate the swap process
            await this.simulateSwap();

            window.uiManager.closeModal('loading');
            window.uiManager.showNotification(
                'Swap Successful',
                `Swapped ${this.fromAmount} ${this.fromToken.symbol} for ${window.Utils.formatNumber(this.toAmount, 6)} ${this.toToken.symbol}`,
                'success'
            );

            // Reset form
            this.resetSwapForm();

        } catch (error) {
            window.uiManager.closeModal('loading');
            window.uiManager.showNotification('Swap Failed', window.Utils.handleError(error), 'error');
        }
    }

    // Simulate swap execution (replace with actual contract interaction)
    async simulateSwap() {
        // Simulate transaction delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // In a real implementation, this would:
        // 1. Check token allowances
        // 2. Approve tokens if needed
        // 3. Execute swap transaction
        // 4. Wait for confirmation
        
        console.log('Simulated swap:', {
            from: this.fromToken,
            to: this.toToken,
            fromAmount: this.fromAmount,
            toAmount: this.toAmount,
            slippage: this.slippage
        });
    }

    // Reset swap form
    resetSwapForm() {
        this.fromAmount = '';
        this.toAmount = '';
        this.priceInfo = null;

        const fromAmountEl = document.getElementById('fromAmount');
        const toAmountEl = document.getElementById('toAmount');

        if (fromAmountEl) fromAmountEl.value = '';
        if (toAmountEl) toAmountEl.value = '';

        this.clearQuote();
        this.updateSwapButton();
        this.updateTokenBalances();
    }
}

// Initialize swap manager
window.SwapManager = new SwapManager();
