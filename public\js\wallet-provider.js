// Ronin Wallet Provider Implementation
// Enhanced wallet connection with support for both Ronin Wallet extension and Ronin Waypoint

class RoninWalletProvider {
    constructor() {
        this.web3 = null;
        this.provider = null;
        this.currentAccount = null;
        this.currentChainId = null;
        this.isConnected = false;
        this.listeners = new Map();
        
        // Initialize event listeners
        this.initializeEventListeners();
    }

    // Initialize wallet provider
    async initialize() {
        try {
            // Check for Ronin Wallet extension first
            if (window.ronin && window.ronin.provider) {
                console.log('Ronin Wallet extension detected');
                this.provider = window.ronin.provider;
                this.web3 = new Web3(this.provider);
                return true;
            }
            
            // Fallback to Ronin Waypoint SDK if available
            if (window.WaypointProvider) {
                console.log('Ronin Waypoint SDK detected');
                this.provider = window.WaypointProvider.create({
                    clientId: 'your-client-id', // Replace with actual client ID
                    chainId: window.DexConfig.TARGET_CHAIN_ID
                });
                this.web3 = new Web3(this.provider);
                return true;
            }
            
            console.warn('No Ronin wallet provider found');
            return false;
        } catch (error) {
            console.error('Error initializing wallet provider:', error);
            return false;
        }
    }

    // Connect to wallet
    async connect() {
        try {
            if (!this.provider) {
                const initialized = await this.initialize();
                if (!initialized) {
                    throw new Error('No Ronin wallet provider available. Please install Ronin Wallet.');
                }
            }

            // Request account access
            const accounts = await this.provider.request({
                method: 'eth_requestAccounts'
            });

            if (accounts.length === 0) {
                throw new Error('No accounts found. Please unlock your wallet.');
            }

            // Get current chain ID
            const chainId = await this.provider.request({
                method: 'eth_chainId'
            });

            // Validate network
            const chainIdNumber = parseInt(chainId, 16);
            const targetChainId = window.DexConfig ? window.DexConfig.TARGET_CHAIN_ID : 2021;
            const networkNames = window.DexConfig ? window.DexConfig.NETWORK_NAMES : { 2021: 'Ronin Saigon Testnet' };

            if (chainIdNumber !== targetChainId) {
                throw new Error(
                    `Wrong network. Please switch to ${networkNames[targetChainId]}`
                );
            }

            // Set connection state
            this.currentAccount = this.web3.utils.toChecksumAddress(accounts[0]);
            this.currentChainId = chainId;
            this.isConnected = true;

            // Store connection state
            localStorage.setItem('kingsDexWalletConnected', 'true');
            localStorage.setItem('kingsDexWalletAddress', this.currentAccount);

            // Emit connection event
            this.emit('connect', {
                account: this.currentAccount,
                chainId: this.currentChainId
            });

            console.log('Wallet connected:', this.currentAccount);
            return {
                account: this.currentAccount,
                chainId: this.currentChainId
            };

        } catch (error) {
            console.error('Error connecting wallet:', error);
            this.emit('error', error);
            throw error;
        }
    }

    // Disconnect wallet
    async disconnect() {
        try {
            this.currentAccount = null;
            this.currentChainId = null;
            this.isConnected = false;

            // Clear stored state
            localStorage.removeItem('kingsDexWalletConnected');
            localStorage.removeItem('kingsDexWalletAddress');

            // Emit disconnect event
            this.emit('disconnect');

            console.log('Wallet disconnected');
        } catch (error) {
            console.error('Error disconnecting wallet:', error);
        }
    }

    // Auto-connect if previously connected
    async autoConnect() {
        try {
            const wasConnected = localStorage.getItem('kingsDexWalletConnected');
            if (!wasConnected) return false;

            const initialized = await this.initialize();
            if (!initialized) return false;

            // Check if still connected
            const accounts = await this.provider.request({
                method: 'eth_accounts'
            });

            if (accounts.length > 0) {
                const chainId = await this.provider.request({
                    method: 'eth_chainId'
                });

                const chainIdNumber = parseInt(chainId, 16);
                const targetChainId = window.DexConfig ? window.DexConfig.TARGET_CHAIN_ID : 2021;

                if (chainIdNumber === targetChainId) {
                    this.currentAccount = this.web3.utils.toChecksumAddress(accounts[0]);
                    this.currentChainId = chainId;
                    this.isConnected = true;

                    this.emit('connect', {
                        account: this.currentAccount,
                        chainId: this.currentChainId
                    });

                    console.log('Auto-connected to wallet:', this.currentAccount);
                    return true;
                }
            }

            return false;
        } catch (error) {
            console.error('Error auto-connecting wallet:', error);
            return false;
        }
    }

    // Switch network
    async switchNetwork(chainId) {
        try {
            if (!this.provider) {
                throw new Error('Wallet not connected');
            }

            const chainIdHex = '0x' + chainId.toString(16);
            
            await this.provider.request({
                method: 'wallet_switchEthereumChain',
                params: [{ chainId: chainIdHex }]
            });

            this.currentChainId = chainIdHex;
            this.emit('chainChanged', chainIdHex);

            return true;
        } catch (error) {
            console.error('Error switching network:', error);
            throw error;
        }
    }

    // Get account balance
    async getBalance(address = null) {
        try {
            if (!this.web3) {
                throw new Error('Web3 not initialized');
            }

            const account = address || this.currentAccount;
            if (!account) {
                throw new Error('No account specified');
            }

            const balanceWei = await this.web3.eth.getBalance(account);
            return this.web3.utils.fromWei(balanceWei, 'ether');
        } catch (error) {
            console.error('Error getting balance:', error);
            throw error;
        }
    }

    // Get token balance
    async getTokenBalance(tokenAddress, accountAddress = null) {
        try {
            if (!this.web3) {
                throw new Error('Web3 not initialized');
            }

            const account = accountAddress || this.currentAccount;
            if (!account) {
                throw new Error('No account specified');
            }

            const token = window.TokenUtils.getTokenByAddress(tokenAddress);
            if (!token) {
                throw new Error('Token not found');
            }

            // For native token, get ETH balance
            if (token.isNative) {
                return await this.getBalance(account);
            }

            // For ERC20 tokens
            const tokenContract = new this.web3.eth.Contract([
                {
                    "constant": true,
                    "inputs": [{"name": "_owner", "type": "address"}],
                    "name": "balanceOf",
                    "outputs": [{"name": "balance", "type": "uint256"}],
                    "type": "function"
                }
            ], tokenAddress);

            const balanceWei = await tokenContract.methods.balanceOf(account).call();
            return window.TokenUtils.fromTokenWei(balanceWei, token.decimals);
        } catch (error) {
            console.error('Error getting token balance:', error);
            throw error;
        }
    }

    // Initialize event listeners for wallet events
    initializeEventListeners() {
        // Listen for account changes
        if (window.ethereum) {
            window.ethereum.on('accountsChanged', (accounts) => {
                if (accounts.length === 0) {
                    this.disconnect();
                } else {
                    this.currentAccount = this.web3.utils.toChecksumAddress(accounts[0]);
                    this.emit('accountsChanged', accounts);
                }
            });

            // Listen for chain changes
            window.ethereum.on('chainChanged', (chainId) => {
                this.currentChainId = chainId;
                this.emit('chainChanged', chainId);
                
                // Check if we're on the correct network
                const chainIdNumber = parseInt(chainId, 16);
                const targetChainId = window.DexConfig ? window.DexConfig.TARGET_CHAIN_ID : 2021;

                if (chainIdNumber !== targetChainId) {
                    this.emit('wrongNetwork', chainIdNumber);
                }
            });

            // Listen for disconnect
            window.ethereum.on('disconnect', () => {
                this.disconnect();
            });
        }
    }

    // Event emitter methods
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Error in event callback:', error);
                }
            });
        }
    }

    // Utility methods
    isWalletConnected() {
        return this.isConnected && this.currentAccount;
    }

    getCurrentAccount() {
        return this.currentAccount;
    }

    getCurrentChainId() {
        return this.currentChainId;
    }

    getWeb3Instance() {
        return this.web3;
    }

    getProvider() {
        return this.provider;
    }
}

// Create global wallet provider instance
window.walletProvider = new RoninWalletProvider();

// Export for use in other modules
window.RoninWalletProvider = RoninWalletProvider;
