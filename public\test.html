<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The King's Dex - Test Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            min-height: 100vh;
        }
    </style>
</head>
<body class="text-white">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-4xl font-bold text-center mb-8 text-cyan-400">The King's Dex - Test Page</h1>
        
        <div class="max-w-2xl mx-auto space-y-6">
            <!-- Configuration Test -->
            <div class="bg-gray-800 rounded-lg p-6 border border-cyan-500">
                <h2 class="text-xl font-bold mb-4">Configuration Test</h2>
                <div id="configTest" class="space-y-2">
                    <p>Loading configuration...</p>
                </div>
            </div>

            <!-- Token List Test -->
            <div class="bg-gray-800 rounded-lg p-6 border border-cyan-500">
                <h2 class="text-xl font-bold mb-4">Token List Test</h2>
                <div id="tokenTest" class="space-y-2">
                    <p>Loading tokens...</p>
                </div>
            </div>

            <!-- Wallet Provider Test -->
            <div class="bg-gray-800 rounded-lg p-6 border border-cyan-500">
                <h2 class="text-xl font-bold mb-4">Wallet Provider Test</h2>
                <div id="walletTest" class="space-y-2">
                    <p>Testing wallet provider...</p>
                </div>
                <button id="testConnectBtn" class="mt-4 bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded">
                    Test Connect
                </button>
            </div>

            <!-- UI Manager Test -->
            <div class="bg-gray-800 rounded-lg p-6 border border-cyan-500">
                <h2 class="text-xl font-bold mb-4">UI Manager Test</h2>
                <div id="uiTest" class="space-y-2">
                    <p>Testing UI manager...</p>
                </div>
                <div class="mt-4 space-x-2">
                    <button id="testNotificationBtn" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded">
                        Test Notification
                    </button>
                    <button id="testModalBtn" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                        Test Modal
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Load our scripts -->
    <script src="js/config.js"></script>
    <script src="js/tokens.js"></script>
    <script src="js/wallet-provider.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/ui.js"></script>

    <script>
        // Test configuration
        function testConfig() {
            const configDiv = document.getElementById('configTest');
            try {
                if (window.DexConfig) {
                    configDiv.innerHTML = `
                        <p class="text-green-400">✓ Configuration loaded successfully</p>
                        <p>Target Chain ID: ${window.DexConfig.TARGET_CHAIN_ID}</p>
                        <p>Network: ${window.DexConfig.NETWORK_NAMES[window.DexConfig.TARGET_CHAIN_ID]}</p>
                        <p>Uniswap V2 Router: ${window.DexConfig.CURRENT_CONTRACTS.UNISWAP_V2_ROUTER}</p>
                    `;
                } else {
                    configDiv.innerHTML = '<p class="text-red-400">✗ Configuration not loaded</p>';
                }
            } catch (error) {
                configDiv.innerHTML = `<p class="text-red-400">✗ Configuration error: ${error.message}</p>`;
            }
        }

        // Test tokens
        function testTokens() {
            const tokenDiv = document.getElementById('tokenTest');
            try {
                if (window.TokenUtils) {
                    const tokens = window.TokenUtils.getCurrentNetworkTokens();
                    tokenDiv.innerHTML = `
                        <p class="text-green-400">✓ Token utilities loaded successfully</p>
                        <p>Available tokens: ${tokens.length}</p>
                        <div class="mt-2">
                            ${tokens.map(token => `
                                <div class="flex items-center space-x-2 text-sm">
                                    <img src="${token.icon}" alt="${token.symbol}" class="w-4 h-4 rounded-full" onerror="this.style.display='none'">
                                    <span>${token.symbol} - ${token.name}</span>
                                </div>
                            `).join('')}
                        </div>
                    `;
                } else {
                    tokenDiv.innerHTML = '<p class="text-red-400">✗ Token utilities not loaded</p>';
                }
            } catch (error) {
                tokenDiv.innerHTML = `<p class="text-red-400">✗ Token utilities error: ${error.message}</p>`;
            }
        }

        // Test wallet provider
        function testWalletProvider() {
            const walletDiv = document.getElementById('walletTest');
            try {
                if (window.walletProvider) {
                    walletDiv.innerHTML = `
                        <p class="text-green-400">✓ Wallet provider loaded successfully</p>
                        <p>Connected: ${window.walletProvider.isWalletConnected()}</p>
                        <p>Current Account: ${window.walletProvider.getCurrentAccount() || 'None'}</p>
                    `;
                } else {
                    walletDiv.innerHTML = '<p class="text-red-400">✗ Wallet provider not loaded</p>';
                }
            } catch (error) {
                walletDiv.innerHTML = `<p class="text-red-400">✗ Wallet provider error: ${error.message}</p>`;
            }
        }

        // Test UI manager
        function testUIManager() {
            const uiDiv = document.getElementById('uiTest');
            try {
                if (window.uiManager) {
                    uiDiv.innerHTML = `
                        <p class="text-green-400">✓ UI manager loaded successfully</p>
                        <p>Current page: ${window.uiManager.currentPage}</p>
                    `;
                } else {
                    uiDiv.innerHTML = '<p class="text-red-400">✗ UI manager not loaded</p>';
                }
            } catch (error) {
                uiDiv.innerHTML = `<p class="text-red-400">✗ UI manager error: ${error.message}</p>`;
            }
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                testConfig();
                testTokens();
                testWalletProvider();
                testUIManager();
            }, 1000);

            // Test connect button
            document.getElementById('testConnectBtn').addEventListener('click', async () => {
                try {
                    if (window.walletProvider) {
                        await window.walletProvider.connect();
                        testWalletProvider();
                    }
                } catch (error) {
                    alert('Connect error: ' + error.message);
                }
            });

            // Test notification button
            document.getElementById('testNotificationBtn').addEventListener('click', () => {
                if (window.uiManager) {
                    window.uiManager.showNotification('Test Notification', 'This is a test notification!', 'success');
                }
            });

            // Test modal button
            document.getElementById('testModalBtn').addEventListener('click', () => {
                if (window.uiManager) {
                    window.uiManager.showInfoModal('Test Modal', 'This is a test modal!');
                }
            });
        });
    </script>
</body>
</html>
