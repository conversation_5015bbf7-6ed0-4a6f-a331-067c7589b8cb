// functions/utils/contracts.js

// --- Ronin Chain Info ---
const RONIN_CHAIN_ID_MAINNET = '2020';
const RONIN_CHAIN_ID_SAIGON = '2021';
// Set this based on your target deployment (or get from env config)
const CURRENT_TARGET_CHAIN_ID = process.env.NETWORK === 'mainnet' ? RONIN_CHAIN_ID_MAINNET : RONIN_CHAIN_ID_SAIGON;

// --- NXS Token (Your Deployed Token) ---
const NXS_TOKEN_ADDRESS = (CURRENT_TARGET_CHAIN_ID === RONIN_CHAIN_ID_MAINNET)
    ? '0xYOUR_NXS_MAINNET_ADDRESS' // Replace with your NXS mainnet address
    : '0xYOUR_NXS_SAIGON_ADDRESS';   // Replace with your NXS Saigon testnet address
const NXS_TOKEN_ABI = [/* --- PASTE YOUR NXS TOKEN ABI JSON HERE --- */];

// --- Uniswap V2 Contracts (Primary DEX - Your Deployed Contracts) ---
const UNISWAP_V2_FACTORY_ADDRESS = (CURRENT_TARGET_CHAIN_ID === RONIN_CHAIN_ID_MAINNET)
    ? '0xYOUR_UNISWAP_V2_FACTORY_MAINNET_ADDRESS' // Will be set after mainnet deployment
    : '0x3b34882989cF524B062470De68D1F4C053C9E98B';   // Deployed to Saigon testnet

const UNISWAP_V2_ROUTER_ADDRESS = (CURRENT_TARGET_CHAIN_ID === RONIN_CHAIN_ID_MAINNET)
    ? '0xYOUR_UNISWAP_V2_ROUTER_MAINNET_ADDRESS' // Will be set after mainnet deployment
    : '0x64edE7475b34511F124Db708941A7767aeeD9a07';   // Deployed to Saigon testnet

// Uniswap V2 Factory ABI
const UNISWAP_V2_FACTORY_ABI = [
    {
        "anonymous": false,
        "inputs": [
            { "indexed": true, "internalType": "address", "name": "token0", "type": "address" },
            { "indexed": true, "internalType": "address", "name": "token1", "type": "address" },
            { "indexed": false, "internalType": "address", "name": "pair", "type": "address" },
            { "indexed": false, "internalType": "uint256", "name": "", "type": "uint256" }
        ],
        "name": "PairCreated",
        "type": "event"
    },
    {
        "constant": true,
        "inputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }],
        "name": "allPairs",
        "outputs": [{ "internalType": "address", "name": "pair", "type": "address" }],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [],
        "name": "allPairsLength",
        "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": false,
        "inputs": [
            { "internalType": "address", "name": "tokenA", "type": "address" },
            { "internalType": "address", "name": "tokenB", "type": "address" }
        ],
        "name": "createPair",
        "outputs": [{ "internalType": "address", "name": "pair", "type": "address" }],
        "payable": false,
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [],
        "name": "feeTo",
        "outputs": [{ "internalType": "address", "name": "", "type": "address" }],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [],
        "name": "feeToSetter",
        "outputs": [{ "internalType": "address", "name": "", "type": "address" }],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [
            { "internalType": "address", "name": "tokenA", "type": "address" },
            { "internalType": "address", "name": "tokenB", "type": "address" }
        ],
        "name": "getPair",
        "outputs": [{ "internalType": "address", "name": "pair", "type": "address" }],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": false,
        "inputs": [{ "internalType": "address", "name": "", "type": "address" }],
        "name": "setFeeTo",
        "outputs": [],
        "payable": false,
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "constant": false,
        "inputs": [{ "internalType": "address", "name": "", "type": "address" }],
        "name": "setFeeToSetter",
        "outputs": [],
        "payable": false,
        "stateMutability": "nonpayable",
        "type": "function"
    }
];

// --- Katana DEX Contracts (Fallback DEX - Ronin Mainnet Addresses) ---
// Replace with actual addresses if different or for Saigon
const KATANA_ROUTER_ADDRESS = (CURRENT_TARGET_CHAIN_ID === RONIN_CHAIN_ID_MAINNET)
    ? '******************************************' // Katana Router V3 (from search)
    : null; // Katana not available on Saigon testnet
const KATANA_ROUTER_ABI = [/* --- PASTE KATANA ROUTER ABI JSON HERE --- */];
// You might also need Katana Factory ABI if you plan to list existing pools dynamically

const KATANA_FACTORY_ADDRESS = (CURRENT_TARGET_CHAIN_ID === RONIN_CHAIN_ID_MAINNET)
    ? '******************************************' // Katana Factory on Ronin Mainnet (same as router for now)
    : null; // Katana not available on Saigon testnet
const KATANA_FACTORY_ABI = [/* --- PASTE KATANA FACTORY ABI (e.g., UniswapV2Factory ABI) --- */];

// Uniswap V2 Router ABI
const UNISWAP_V2_ROUTER_ABI = [
    {
        "inputs": [],
        "name": "WETH",
        "outputs": [{ "internalType": "address", "name": "", "type": "address" }],
        "stateMutability": "pure",
        "type": "function"
    },
    {
        "inputs": [
            { "internalType": "address", "name": "tokenA", "type": "address" },
            { "internalType": "address", "name": "tokenB", "type": "address" },
            { "internalType": "uint256", "name": "amountADesired", "type": "uint256" },
            { "internalType": "uint256", "name": "amountBDesired", "type": "uint256" },
            { "internalType": "uint256", "name": "amountAMin", "type": "uint256" },
            { "internalType": "uint256", "name": "amountBMin", "type": "uint256" },
            { "internalType": "address", "name": "to", "type": "address" },
            { "internalType": "uint256", "name": "deadline", "type": "uint256" }
        ],
        "name": "addLiquidity",
        "outputs": [
            { "internalType": "uint256", "name": "amountA", "type": "uint256" },
            { "internalType": "uint256", "name": "amountB", "type": "uint256" },
            { "internalType": "uint256", "name": "liquidity", "type": "uint256" }
        ],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "factory",
        "outputs": [{ "internalType": "address", "name": "", "type": "address" }],
        "stateMutability": "pure",
        "type": "function"
    },
    {
        "inputs": [
            { "internalType": "uint256", "name": "amountOut", "type": "uint256" },
            { "internalType": "uint256", "name": "reserveIn", "type": "uint256" },
            { "internalType": "uint256", "name": "reserveOut", "type": "uint256" }
        ],
        "name": "getAmountIn",
        "outputs": [{ "internalType": "uint256", "name": "amountIn", "type": "uint256" }],
        "stateMutability": "pure",
        "type": "function"
    },
    {
        "inputs": [
            { "internalType": "uint256", "name": "amountIn", "type": "uint256" },
            { "internalType": "uint256", "name": "reserveIn", "type": "uint256" },
            { "internalType": "uint256", "name": "reserveOut", "type": "uint256" }
        ],
        "name": "getAmountOut",
        "outputs": [{ "internalType": "uint256", "name": "amountOut", "type": "uint256" }],
        "stateMutability": "pure",
        "type": "function"
    },
    {
        "inputs": [
            { "internalType": "uint256", "name": "amountOut", "type": "uint256" },
            { "internalType": "address[]", "name": "path", "type": "address[]" }
        ],
        "name": "getAmountsIn",
        "outputs": [{ "internalType": "uint256[]", "name": "amounts", "type": "uint256[]" }],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [
            { "internalType": "uint256", "name": "amountIn", "type": "uint256" },
            { "internalType": "address[]", "name": "path", "type": "address[]" }
        ],
        "name": "getAmountsOut",
        "outputs": [{ "internalType": "uint256[]", "name": "amounts", "type": "uint256[]" }],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [
            { "internalType": "uint256", "name": "amountIn", "type": "uint256" },
            { "internalType": "uint256", "name": "amountOutMin", "type": "uint256" },
            { "internalType": "address[]", "name": "path", "type": "address[]" },
            { "internalType": "address", "name": "to", "type": "address" },
            { "internalType": "uint256", "name": "deadline", "type": "uint256" }
        ],
        "name": "swapExactTokensForTokens",
        "outputs": [{ "internalType": "uint256[]", "name": "amounts", "type": "uint256[]" }],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [
            { "internalType": "uint256", "name": "amountIn", "type": "uint256" },
            { "internalType": "uint256", "name": "amountOutMin", "type": "uint256" },
            { "internalType": "address[]", "name": "path", "type": "address[]" },
            { "internalType": "address", "name": "to", "type": "address" },
            { "internalType": "uint256", "name": "deadline", "type": "uint256" }
        ],
        "name": "swapExactTokensForTokensSupportingFeeOnTransferTokens",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
    }
];

// --- DEX Protocol Configuration ---
const DEX_PROTOCOLS = {
    UNISWAP_V2: 'uniswap_v2',
    KATANA: 'katana'
};

// Minimal ERC20 ABI for common interactions
const ERC20_ABI_MINIMAL = [
    { "constant": true, "inputs": [{ "name": "_owner", "type": "address" }], "name": "balanceOf", "outputs": [{ "name": "balance", "type": "uint256" }], "type": "function" },
    { "constant": true, "inputs": [], "name": "decimals", "outputs": [{ "name": "", "type": "uint8" }], "type": "function" },
    { "constant": true, "inputs": [], "name": "symbol", "outputs": [{ "name": "", "type": "string" }], "type": "function" },
    { "constant": true, "inputs": [], "name": "name", "outputs": [{ "name": "", "type": "string" }], "type": "function" },
    { "constant": true, "inputs": [{ "name": "_owner", "type": "address" }, { "name": "_spender", "type": "address" }], "name": "allowance", "outputs": [{ "name": "remaining", "type": "uint256" }], "type": "function" },
    { "constant": false, "inputs": [{ "name": "_spender", "type": "address" }, { "name": "_value", "type": "uint256" }], "name": "approve", "outputs": [{ "name": "success", "type": "bool" }], "type": "function" }
];

// LP Token ABI (UniswapV2Pair ABI is common)
const LP_TOKEN_ABI = [
    {"constant":true,"inputs":[],"name":"token0","outputs":[{"internalType":"address","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},
    {"constant":true,"inputs":[],"name":"token1","outputs":[{"internalType":"address","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},
    {"constant":true,"inputs":[],"name":"getReserves","outputs":[{"internalType":"uint112","name":"_reserve0","type":"uint112"},{"internalType":"uint112","name":"_reserve1","type":"uint112"},{"internalType":"uint32","name":"_blockTimestampLast","type":"uint32"}],"payable":false,"stateMutability":"view","type":"function"},
    {"constant":true,"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},
    // ... add balanceOf, decimals, symbol, name if needed for LP tokens
];


// --- The King's Dex Custom Contracts (Phase 2 - Your Deployed Contracts) ---
// const KINGS_DEX_V2_FACTORY_ADDRESS = '0xYOUR_KD_V2_FACTORY_ADDRESS';
// const KINGS_DEX_V2_FACTORY_ABI = [/* ... */];
// const KINGS_DEX_V2_ROUTER_ADDRESS = '0xYOUR_KD_V2_ROUTER_ADDRESS';
// const KINGS_DEX_V2_ROUTER_ABI = [/* ... */];
// const KINGS_DEX_FARM_CONTROLLER_ADDRESS = '0xYOUR_KD_FARM_CONTROLLER_ADDRESS';
// const KINGS_DEX_FARM_CONTROLLER_ABI = [/* ... */];


module.exports = {
    RONIN_CHAIN_ID_MAINNET,
    RONIN_CHAIN_ID_SAIGON,
    CURRENT_TARGET_CHAIN_ID,
    NXS_TOKEN_ADDRESS,
    NXS_TOKEN_ABI,
    // Uniswap V2 (Primary DEX)
    UNISWAP_V2_FACTORY_ADDRESS,
    UNISWAP_V2_FACTORY_ABI,
    UNISWAP_V2_ROUTER_ADDRESS,
    UNISWAP_V2_ROUTER_ABI,
    // Katana DEX (Fallback)
    KATANA_ROUTER_ADDRESS,
    KATANA_ROUTER_ABI,
    KATANA_FACTORY_ADDRESS,
    KATANA_FACTORY_ABI,
    // Common ABIs
    ERC20_ABI_MINIMAL,
    LP_TOKEN_ABI,
    // DEX Configuration
    DEX_PROTOCOLS,
    // KINGS_DEX_V2_FACTORY_ADDRESS, KINGS_DEX_V2_FACTORY_ABI,
    // KINGS_DEX_V2_ROUTER_ADDRESS, KINGS_DEX_V2_ROUTER_ABI,
    // KINGS_DEX_FARM_CONTROLLER_ADDRESS, KINGS_DEX_FARM_CONTROLLER_ABI,
};
