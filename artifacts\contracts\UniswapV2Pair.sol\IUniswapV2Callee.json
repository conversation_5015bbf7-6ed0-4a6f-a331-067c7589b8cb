{"_format": "hh-sol-artifact-1", "contractName": "IUniswapV2Callee", "sourceName": "contracts/UniswapV2Pair.sol", "abi": [{"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "amount0", "type": "uint256"}, {"internalType": "uint256", "name": "amount1", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "uniswapV2Call", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}